import Link from 'next/link';
import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import ImagePlaceholder from './ImagePlaceholder';

// Throttle function for performance optimization
const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function(this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

interface EventCardProps {
  id: string;
  title: string;
  date: string;
  location?: string;
  image?: string;
  className?: string;
}

export default function EventCard({
  id,
  title,
  date,
  location,
  className = '',
}: EventCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 50, y: 50 });
  const cardRef = useRef<HTMLDivElement>(null);
  const isVisibleRef = useRef(true);

  // Simplified time calculation without separate animation loop
  const time = Date.now() / 1000;

  // Visibility API for performance optimization
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Optimized mouse tracking with throttling
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleMouseMove = useCallback(
    throttle((e: React.MouseEvent) => {
      if (cardRef.current && isHovered) { // Only track when hovered
        const rect = cardRef.current.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;
        setMousePosition({ x, y });
      }
    }, 32), // 30fps throttling
    [isHovered]
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
    const day = date.getDate();
    return { month, day };
  };

  const { month, day } = formatDate(date);

  // Simplified and memoized style calculations
  const cardTransform = useMemo(() => {
    if (!isHovered) return 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px) scale(1)';

    const rotateX = (mousePosition.y - 50) * 0.05; // Reduced intensity
    const rotateY = (mousePosition.x - 50) * 0.05; // Reduced intensity
    return `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px) scale(1.02)`;
  }, [isHovered, mousePosition.x, mousePosition.y]);

  const chromaticBackground = useMemo(() => {
    if (!isHovered) return 'transparent';
    return `
      radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%,
        rgba(255, 0, 128, 0.1) 0%,
        rgba(0, 255, 255, 0.08) 30%,
        rgba(255, 255, 0, 0.05) 60%,
        transparent 100%
      )
    `;
  }, [isHovered, mousePosition.x, mousePosition.y]);

  // Simplified background without complex time-based animations
  const liquidGlassBackground = useMemo(() => {
    if (!isHovered) return 'transparent';
    return `
      linear-gradient(135deg,
        rgba(255, 0, 128, 0.1) 0%,
        rgba(0, 255, 255, 0.1) 50%,
        rgba(255, 255, 0, 0.1) 100%
      )
    `;
  }, [isHovered]);

  return (
    <div
      ref={cardRef}
      className={`group cursor-glow transition-all duration-700 rounded-3xl overflow-hidden card-3d ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseMove={handleMouseMove}
      style={{
        transform: cardTransform,
      }}
    >
      {/* Ultra Glass Container with Liquid Distortion */}
      <div className="relative h-full glass-ultra-premium backdrop-blur-3xl border border-white/20 rounded-3xl overflow-hidden">
        {/* Chromatic Aberration Layer - Reduced opacity for better text readability */}
        <div
          className="absolute inset-0 opacity-15 group-hover:opacity-25 transition-opacity duration-500 pointer-events-none"
          style={{
            background: chromaticBackground,
            filter: 'blur(2px)',
            mixBlendMode: 'overlay',
          }}
        />

        {/* Liquid Glass Distortion - Reduced intensity for better readability */}
        <div
          className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-all duration-700 pointer-events-none"
          style={{
            background: liquidGlassBackground,
            filter: 'blur(3px) saturate(120%)',
            transform: `scale(${1 + Math.sin(time) * 0.03})`,
          }}
        />

        {/* Event Image with Advanced Effects */}
        <div className="relative mb-6 overflow-hidden">
          <div className="relative group/image">
            <ImagePlaceholder
              aspectRatio="landscape"
              className="w-full rounded-t-3xl transition-all duration-500 group-hover:brightness-110"
              alt={`${title} event image`}
              style={{
                transform: isHovered ? 'scale(1.05)' : 'scale(1)',
                filter: isHovered
                  ? 'saturate(110%) contrast(102%)'
                  : 'saturate(100%) contrast(100%)',
              }}
            />

            {/* Simplified Overlay Effect */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
              style={{
                background: 'linear-gradient(45deg, rgba(255, 0, 128, 0.1) 0%, rgba(0, 255, 255, 0.1) 100%)',
                mixBlendMode: 'overlay',
              }}
            />

            {/* Holographic Scan Lines */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
              style={{
                background: `
                  repeating-linear-gradient(
                    0deg,
                    transparent 0px,
                    rgba(255, 255, 255, 0.1) 1px,
                    transparent 2px,
                    transparent 4px
                  )
                `,
                transform: `translateY(${Math.sin(time * 3) * 10}px)`,
              }}
            />
          </div>

          {/* Simplified Date Badge */}
          <div
            className="absolute top-4 left-4 glass-dark rounded-2xl p-4 text-center min-w-[80px] transition-all duration-300"
            style={{
              transform: isHovered ? 'scale(1.05)' : 'scale(1)',
              boxShadow: isHovered
                ? '0 0 20px rgba(0, 255, 255, 0.4)'
                : '0 0 10px rgba(0, 255, 255, 0.2)',
            }}
          >
            <div className="text-xs font-bold text-neon-cyan uppercase tracking-wider leading-none mb-1 animate-pulse-glow">
              {month}
            </div>
            <div
              className="text-2xl font-black text-white leading-none"
              style={{
                textShadow: isHovered
                  ? `0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.6)`
                  : '0 0 10px rgba(255, 255, 255, 0.5)',
              }}
            >
              {day}
            </div>
          </div>

          {/* Simplified Gradient Overlay */}
          <div
            className="absolute inset-0 opacity-0 group-hover:opacity-60 transition-all duration-500 rounded-t-3xl pointer-events-none"
            style={{
              background: `
                linear-gradient(
                  135deg,
                  rgba(0, 0, 0, 0.6) 0%,
                  rgba(255, 0, 128, 0.15) 40%,
                  transparent 100%
                )
              `,
            }}
          />

          {/* Morphing Floating Action Button - TEMPORARILY COMMENTED OUT */}
          {/* <div
            className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-700 transform translate-y-8 group-hover:translate-y-0"
            style={{
              transform: isHovered
                ? `translateY(0px) scale(1.1) rotate(${Math.sin(time * 2) * 10}deg)`
                : 'translateY(32px) scale(1) rotate(0deg)',
            }}
          >
            <div
              className="relative w-14 h-14 rounded-full flex items-center justify-center cursor-pointer micro-haptic"
              style={{
                background: `
                  conic-gradient(from ${time * 50}deg,
                    rgba(255, 0, 128, 0.9) 0deg,
                    rgba(0, 255, 255, 0.9) 120deg,
                    rgba(255, 255, 0, 0.9) 240deg,
                    rgba(255, 0, 128, 0.9) 360deg
                  )
                `,
                boxShadow: `
                  0 0 30px rgba(255, 0, 128, 0.6),
                  0 0 60px rgba(0, 255, 255, 0.4),
                  inset 0 0 20px rgba(255, 255, 255, 0.2)
                `,
                animation: 'pulse 2s ease-in-out infinite',
              }}
            >
              {isHovered && [...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-white rounded-full pointer-events-none animate-ping"
                  style={{
                    left: `${30 + i * 15}%`,
                    top: `${30 + i * 15}%`,
                    opacity: 0.6 - (i * 0.1),
                    animationDelay: `${i * 0.2}s`,
                  }}
                />
              ))}

              <svg
                className="w-7 h-7 text-black transition-transform duration-300"
                fill="currentColor"
                viewBox="0 0 20 20"
                style={{
                  transform: isHovered ? 'scale(1.2) rotate(45deg)' : 'scale(1) rotate(0deg)',
                }}
              >
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div> */}
        </div>

        {/* Enhanced Event Details with Liquid Glass Background */}
        <div
          className="space-y-6 p-8 relative"
          style={{
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.05) 0%,
                rgba(255, 255, 255, 0.02) 50%,
                rgba(255, 255, 255, 0.08) 100%
              )
            `,
            backdropFilter: 'blur(20px) saturate(180%)',
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          {/* Simplified Gradient Background */}
          <div
            className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
            style={{
              background: liquidGlassBackground,
            }}
          />

          <div className="space-y-4 relative z-10">
            {/* Holographic Title with Chromatic Text - Fixed visibility issue */}
            <h3
              className={`text-2xl font-black line-clamp-2 uppercase tracking-wide transition-all duration-700 relative ${
                isHovered
                  ? 'text-transparent bg-clip-text bg-gradient-to-r from-white via-neon-pink via-neon-cyan to-white'
                  : 'text-white'
              }`}
              style={{
                textShadow: isHovered
                  ? '0 0 10px rgba(0, 0, 0, 0.8), 0 0 20px rgba(255, 255, 255, 0.3)'
                  : '0 0 20px rgba(255, 255, 255, 0.5)',
                transform: isHovered ? `scale(1.05) translateY(-2px)` : 'scale(1) translateY(0px)',
                // Ensure text is always visible with fallback
                WebkitTextStroke: isHovered ? '1px rgba(255, 255, 255, 0.3)' : 'none',
              }}
            >
              {title}
            </h3>

            {/* Enhanced Location with Animated Icon */}
            {location && (
              <p className="text-sm text-white/80 flex items-center font-medium group-hover:text-white transition-colors duration-500">
                <svg
                  className="w-5 h-5 mr-3 text-neon-cyan transition-all duration-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{
                    transform: isHovered ? 'scale(1.2) rotate(10deg)' : 'scale(1) rotate(0deg)',
                    filter: isHovered ? 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.8))' : 'none',
                  }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span
                  style={{
                    textShadow: isHovered ? '0 0 10px rgba(0, 255, 255, 0.6)' : 'none',
                  }}
                >
                  {location}
                </span>
              </p>
            )}
          </div>

          {/* Simplified View Details Link */}
          <Link
            href={`/events/${id}`}
            className="inline-flex items-center text-sm font-bold uppercase tracking-wide transition-all duration-300 relative group/link"
            style={{
              color: isHovered ? 'rgba(0, 255, 255, 1)' : 'rgba(255, 0, 128, 1)',
              textShadow: isHovered
                ? '0 0 15px rgba(0, 255, 255, 0.6)'
                : '0 0 8px rgba(255, 0, 128, 0.4)',
              transform: isHovered ? 'translateX(3px)' : 'translateX(0px)',
            }}
          >
            {/* Animated Background */}
            <div
              className="absolute inset-0 opacity-0 group-hover/link:opacity-20 transition-opacity duration-300 rounded-lg pointer-events-none"
              style={{
                background: `
                  linear-gradient(90deg,
                    rgba(255, 0, 128, 0.3) 0%,
                    rgba(0, 255, 255, 0.3) 100%
                  )
                `,
                transform: `scaleX(${isHovered ? 1 : 0})`,
                transformOrigin: 'left',
              }}
            />

            <span className="relative z-10">View Details</span>

            <svg
              className="w-5 h-5 ml-3 transition-all duration-500 relative z-10"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
              style={{
                transform: isHovered
                  ? `translateX(8px) rotate(${Math.sin(time * 3) * 15}deg) scale(1.2)`
                  : 'translateX(0px) rotate(0deg) scale(1)',
                filter: isHovered ? 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.8))' : 'none',
              }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
}
