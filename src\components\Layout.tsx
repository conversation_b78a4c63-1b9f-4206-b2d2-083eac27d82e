import { ReactNode } from 'react';
import SEO from './SEO';
import dynamic from 'next/dynamic';

// Load client-only components to avoid SSR issues
const Navigation = dynamic(() => import('./Navigation'), { ssr: false });
const Footer = dynamic(() => import('./Footer'), { ssr: false });
const EnhancedCustomCursor = dynamic(() => import('./EnhancedCustomCursor'), { ssr: false });
const ScrollAnimations = dynamic(() => import('./ScrollAnimations'), { ssr: false });
const PerformanceOptimizer = dynamic(() => import('./PerformanceOptimizer'), { ssr: false });

interface LayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
  className?: string;
}

export default function Layout({
  children,
  title,
  description,
  image,
  url,
  type,
  noindex,
  className = '',
}: LayoutProps) {
  return (
    <>
      <SEO
        title={title}
        description={description}
        image={image}
        url={url}
        type={type}
        noindex={noindex}
      />

      <div className={`min-h-screen flex flex-col ${className} relative`}>
        {/* Performance Optimizer */}
        <PerformanceOptimizer />

        {/* Enhanced Custom Cursor with Advanced Effects - DISABLED: Reverting to default cursor */}
        {/* <EnhancedCustomCursor /> */}

        {/* Scroll Animations */}
        <ScrollAnimations />

        {/* Navigation */}
        <Navigation />

        {/* Main content */}
        <main className="flex-grow pt-16 relative z-10">
          {children}
        </main>

        {/* Footer */}
        <Footer />
      </div>
    </>
  );
}
