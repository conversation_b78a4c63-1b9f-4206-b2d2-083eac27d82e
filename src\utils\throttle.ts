/**
 * Throttle function to limit the rate at which a function can fire
 * @param func - The function to throttle
 * @param limit - The time limit in milliseconds
 * @returns Throttled function
 */
export const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  let lastFunc: NodeJS.Timeout;
  let lastRan: number;

  return function(this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      lastRan = Date.now();
      inThrottle = true;
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if ((Date.now() - lastRan) >= limit) {
          func.apply(this, args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  }
};

/**
 * Simple throttle function for basic use cases
 * @param func - The function to throttle
 * @param limit - The time limit in milliseconds
 * @returns Throttled function
 */
export const simpleThrottle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function(this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

/**
 * Performance-optimized throttle using requestAnimationFrame
 * Perfect for mouse move handlers and animations
 * @param func - The function to throttle
 * @param fps - Target frames per second (default: 10)
 * @returns Throttled function
 */
export const rafThrottle = (func: Function, fps: number = 10) => {
  let rafId: number | null = null;
  let lastTime = 0;
  const interval = 1000 / fps;

  return function(this: any, ...args: any[]) {
    const now = performance.now();

    if (now - lastTime >= interval) {
      lastTime = now;
      func.apply(this, args);
    } else if (!rafId) {
      rafId = requestAnimationFrame(() => {
        rafId = null;
        const currentTime = performance.now();
        if (currentTime - lastTime >= interval) {
          lastTime = currentTime;
          func.apply(this, args);
        }
      });
    }
  };
};
