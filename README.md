# Conscious Collective

A modern, responsive website for the Conscious Collective - cultivating intentional experiences in Southern California.

## Features

- 🎨 Modern design with custom gradient accents and gold highlights
- 📱 Fully responsive design optimized for all devices
- ⚡ Fast static site generation with Next.js
- 🎭 Interactive components with smooth animations
- 🖼️ Masonry grid for past events with lightbox functionality
- 📧 Contact forms with email capture
- 🎪 Event carousel with horizontal scrolling
- 🌙 Dark mode support
- 📈 Analytics integration with Vercel

## Tech Stack

- **Framework**: Next.js 13+ with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Deployment**: Vercel
- **Analytics**: Vercel Analytics
- **Icons**: Heroicons (via SVG)

## Design System

### Colors
- **Primary Background**: #FFFFFF (light) / #0F0F0F (dark)
- **Text**: #111827 (primary) / #6B7280 (secondary)
- **Accent Gradient**: linear-gradient(135deg, #8456EC 0%, #FF4ACD 100%)
- **Gold Highlight**: #FFB800

### Typography
- **Font**: Inter (with <PERSON><PERSON><PERSON> fallback)
- **H1**: 64px, weight 800, letter-spacing -0.02em
- **H2**: 40px, weight 700
- **H3**: 28px, weight 600
- **Body**: 18px, weight 400, line-height 1.6

### Spacing
- **Base Grid**: 8pt system
- **Section Padding**: 96px desktop → 48px mobile
- **Content Max-Width**: 1280px with 24px gutters

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/consciouscollective.git
cd consciouscollective
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Building for Production

```bash
npm run build
npm start
```

## Project Structure

```
├── public/                  # Static assets
├── src/
│   ├── components/         # React components
│   │   ├── Layout.tsx     # Main layout wrapper
│   │   ├── Hero.tsx       # Hero section with video
│   │   ├── FeaturedEvents.tsx # Event carousel
│   │   ├── About.tsx      # About section with Polaroids
│   │   ├── MerchSection.tsx # Coming soon merch
│   │   ├── PastEvents.tsx # Masonry grid with filters
│   │   ├── PartyWithUs.tsx # Contact form
│   │   └── ...
│   ├── pages/             # Next.js pages
│   │   ├── index.tsx      # Homepage
│   │   ├── events/[id].tsx # Dynamic event pages
│   │   ├── 404.tsx        # Custom 404 page
│   │   └── _app.tsx       # App wrapper
│   └── styles/            # CSS styles
│       └── globals.css    # Global styles and components
├── tailwind.config.js     # Tailwind configuration
└── next.config.js         # Next.js configuration
```

## Components

### Core Components
- **Layout**: Main layout wrapper with SEO and footer
- **SEO**: Meta tags and Open Graph configuration
- **ImagePlaceholder**: Reusable image placeholder component
- **VideoPlaceholder**: Video placeholder with play button overlay

### Section Components
- **Hero**: Full-screen video background with CTA buttons
- **FeaturedEvents**: Horizontal scrolling event carousel
- **About**: Two-column layout with Polaroid-style image collage
- **MerchSection**: Email capture form with product preview
- **PastEvents**: Masonry grid with category filters and lightbox
- **PartyWithUs**: Contact form with video background
- **Footer**: Site footer with navigation and social links

## Deployment

The site is configured for deployment on Vercel:

1. Connect your GitHub repository to Vercel
2. Vercel will automatically detect the Next.js configuration
3. Deploy with zero configuration needed

### Environment Variables

No environment variables are required for basic functionality. Add any API keys or external service configurations as needed.

## Customization

### Adding New Events

Events are currently mock data in the components. To add a CMS or database:

1. Replace mock data in `FeaturedEvents.tsx` and `events/[id].tsx`
2. Add API routes in `pages/api/` for data fetching
3. Update `getStaticProps` and `getStaticPaths` in dynamic pages

### Styling

The design system is configured in `tailwind.config.js`. Key customizations:

- Custom color palette with gradient utilities
- Typography scale with responsive variants
- 8pt spacing system
- Component-specific utilities

### Adding New Sections

1. Create component in `src/components/`
2. Import and add to homepage in `src/pages/index.tsx`
3. Add any new styles to `globals.css`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is private and proprietary to Conscious Collective.
