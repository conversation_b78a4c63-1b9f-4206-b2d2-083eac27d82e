'use client';

import { useState, useEffect, useRef } from 'react';
import HyperBadge from '../HyperBadge';
import HyperButton from '../HyperButton';
import { useAnimation } from '../../utils/AnimationManager';

export default function PartnerWithUs() {
  const [isVisible, setIsVisible] = useState(false);
  const [activePartnership, setActivePartnership] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    partnershipType: '',
    message: ''
  });
  const [time, setTime] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  useAnimation('partner-with-us', (currentTime) => {
    setTime(currentTime);
  }, 8);

  const partnershipTypes = [
    {
      title: 'Venue Partners',
      subtitle: 'Space Collaboration',
      description: 'Transform your venue into a conscious celebration space. We bring the vision, community, and production expertise.',
      benefits: ['Increased Revenue', 'Brand Alignment', 'Community Building', 'Marketing Support'],
      icon: '🏛️',
      color: 'from-neon-pink to-neon-purple'
    },
    {
      title: 'Brand Sponsors',
      subtitle: 'Conscious Marketing',
      description: 'Align your brand with authentic experiences and conscious community. Reach engaged audiences through meaningful partnerships.',
      benefits: ['Authentic Engagement', 'Community Access', 'Brand Elevation', 'Content Creation'],
      icon: '🤝',
      color: 'from-neon-cyan to-neon-blue'
    },
    {
      title: 'Artist Collaborations',
      subtitle: 'Creative Synergy',
      description: 'Join our roster of conscious artists and performers. We provide platforms, promotion, and community for your artistic expression.',
      benefits: ['Performance Opportunities', 'Community Support', 'Professional Growth', 'Creative Freedom'],
      icon: '🎨',
      color: 'from-neon-green to-neon-yellow'
    },
    {
      title: 'Wellness Partners',
      subtitle: 'Holistic Integration',
      description: 'Integrate wellness practices into our events. From sound healing to meditation, help us create truly transformative experiences.',
      benefits: ['Platform Exposure', 'Community Impact', 'Collaborative Events', 'Wellness Advocacy'],
      icon: '🧘',
      color: 'from-neon-yellow to-neon-orange'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    // Auto-rotate partnerships
    const partnerTimer = setInterval(() => {
      setActivePartnership(prev => (prev + 1) % partnershipTypes.length);
    }, 5000);

    return () => {
      observer.disconnect();
      clearInterval(partnerTimer);
    };
  }, [partnershipTypes.length]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send to your backend
    console.log('Partnership inquiry submitted:', formData);
    alert('Thank you for your interest! We\'ll be in touch soon.');
    setFormData({
      name: '',
      email: '',
      company: '',
      partnershipType: '',
      message: ''
    });
  };

  return (
    <section 
      id="partner-with-us"
      ref={sectionRef}
      className="section-padding-large bg-black relative overflow-hidden"
    >
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Central Partnership Energy */}
        <div
          className="absolute w-[900px] h-[900px] rounded-full opacity-12 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.3) 0%, rgba(0, 255, 255, 0.2) 40%, rgba(0, 255, 65, 0.1) 70%, transparent 100%)',
            left: '50%',
            top: '50%',
            transform: `translate(-50%, -50%) scale(${1 + Math.sin(time * 0.4) * 0.2}) rotate(${time * 0.5}deg)`,
          }}
        />

        {/* Orbiting Partnership Blobs */}
        {partnershipTypes.map((_, index) => (
          <div
            key={index}
            className="absolute w-32 h-32 rounded-full opacity-10 blur-2xl"
            style={{
              background: `radial-gradient(circle, ${
                index % 4 === 0 ? 'rgba(255, 0, 128, 0.4)' :
                index % 4 === 1 ? 'rgba(0, 255, 255, 0.4)' :
                index % 4 === 2 ? 'rgba(0, 255, 65, 0.4)' :
                'rgba(255, 255, 0, 0.4)'
              } 0%, transparent 70%)`,
              left: `${50 + Math.cos((time * 0.15) + (index * Math.PI * 2 / partnershipTypes.length)) * 40}%`,
              top: `${50 + Math.sin((time * 0.15) + (index * Math.PI * 2 / partnershipTypes.length)) * 40}%`,
              transform: `translate(-50%, -50%) scale(${activePartnership === index ? 1.5 : 1})`,
              transition: 'transform 0.5s ease-out',
            }}
          />
        ))}

        {/* Connection Lines */}
        <div 
          className="absolute inset-0 opacity-8"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 0, 128, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '150px 150px',
            transform: `translateX(${Math.sin(time * 0.05) * 30}px) translateY(${Math.cos(time * 0.05) * 30}px)`,
          }}
        />
      </div>

      <div className="content-container relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-8">
            <HyperBadge
              type="vip"
              text="PARTNERSHIPS"
              size="lg"
            />
          </div>

          <h2 className="text-display-lg md:text-display-md font-black text-white leading-none tracking-tight mb-8">
            PARTNER WITH
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-green animate-gradient-x">
              collectiv
            </span>
          </h2>

          <p className="text-xl md:text-2xl font-medium text-white/70 leading-relaxed max-w-4xl mx-auto">
            Join us in revolutionizing nightlife and conscious celebration. Whether you&apos;re a venue, brand, artist, or wellness practitioner — 
            let&apos;s create transformative experiences together.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Left - Partnership Types */}
          <div className={`space-y-8 transition-all duration-1000 delay-200 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <h3 className="text-2xl font-black text-white mb-8">
              PARTNERSHIP
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan to-neon-green"> OPPORTUNITIES</span>
            </h3>

            {partnershipTypes.map((partnership, index) => (
              <div
                key={index}
                className={`relative p-6 rounded-2xl border border-white/10 cursor-pointer transition-all duration-500 ${
                  activePartnership === index 
                    ? 'glass-ultra scale-105' 
                    : 'glass hover:glass-ultra'
                }`}
                onClick={() => setActivePartnership(index)}
              >
                {/* Active Indicator */}
                {activePartnership === index && (
                  <div className="absolute -left-3 top-1/2 transform -translate-y-1/2">
                    <div className={`w-6 h-6 rounded-full bg-gradient-to-r ${partnership.color} animate-pulse-glow`}>
                      <div className="w-full h-full bg-white rounded-full scale-50"></div>
                    </div>
                  </div>
                )}

                <div className="flex items-start space-x-4">
                  {/* Icon */}
                  <div className={`w-14 h-14 rounded-xl bg-gradient-to-r ${partnership.color} flex items-center justify-center text-xl flex-shrink-0 transition-transform duration-300 ${
                    activePartnership === index ? 'scale-110' : ''
                  }`}>
                    {partnership.icon}
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <div className="mb-3">
                      <h4 className="text-lg font-black text-white mb-1">{partnership.title}</h4>
                      <p className="text-sm font-medium text-white/60">{partnership.subtitle}</p>
                    </div>
                    <p className="text-white/70 text-sm leading-relaxed mb-4">{partnership.description}</p>
                    
                    {/* Benefits */}
                    <div className="flex flex-wrap gap-2">
                      {partnership.benefits.map((benefit, i) => (
                        <span 
                          key={i}
                          className="px-3 py-1 bg-white/5 rounded-full text-xs font-medium text-white/70 border border-white/10"
                        >
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Hover Particles */}
                {activePartnership === index && (
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(8)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                        style={{
                          left: `${15 + Math.random() * 70}%`,
                          top: `${15 + Math.random() * 70}%`,
                          animationDelay: `${Math.random() * 0.8}s`,
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right - Contact Form */}
          <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="glass-ultra rounded-3xl p-8 border border-white/20">
              <h3 className="text-2xl font-black text-white mb-6 text-center">
                START THE
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink to-neon-cyan"> CONVERSATION</span>
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Name *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:border-neon-cyan focus:bg-white/10 transition-all duration-300"
                    placeholder="Your full name"
                  />
                </div>

                {/* Email */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Email *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:border-neon-cyan focus:bg-white/10 transition-all duration-300"
                    placeholder="<EMAIL>"
                  />
                </div>

                {/* Company */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Company/Organization</label>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:border-neon-cyan focus:bg-white/10 transition-all duration-300"
                    placeholder="Your company or organization"
                  />
                </div>

                {/* Partnership Type */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Partnership Interest *</label>
                  <select
                    name="partnershipType"
                    value={formData.partnershipType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white focus:outline-none focus:border-neon-cyan focus:bg-white/10 transition-all duration-300"
                  >
                    <option value="">Select partnership type</option>
                    <option value="venue">Venue Partnership</option>
                    <option value="brand">Brand Sponsorship</option>
                    <option value="artist">Artist Collaboration</option>
                    <option value="wellness">Wellness Partnership</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Message *</label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:border-neon-cyan focus:bg-white/10 transition-all duration-300 resize-none"
                    placeholder="Tell us about your vision and how you'd like to collaborate with Collectiv..."
                  />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <HyperButton
                    variant="plasma"
                    size="xl"
                    onClick={() => {}}
                    icon="🚀"
                    soundEnabled={true}
                    className="w-full"
                  >
                    Send Partnership Inquiry
                  </HyperButton>
                </div>
              </form>

              {/* Contact Info */}
              <div className="mt-8 pt-8 border-t border-white/10 text-center">
                <p className="text-white/60 text-sm mb-4">
                  Prefer to reach out directly?
                </p>
                <div className="space-y-2">
                  <p className="text-white/80 text-sm">
                    <span className="font-medium">Email:</span> <EMAIL>
                  </p>
                  <p className="text-white/80 text-sm">
                    <span className="font-medium">Phone:</span> (619) 555-VIBE
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success Stories */}
        <div className={`text-center mt-20 transition-all duration-1000 delay-600 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="glass-ultra rounded-3xl p-12 border border-white/20 max-w-5xl mx-auto">
            <h3 className="text-3xl font-black text-white mb-6">
              PARTNERSHIP
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-green to-neon-cyan"> SUCCESS</span>
            </h3>
            <p className="text-white/70 text-lg mb-8 leading-relaxed">
              Our partnerships have created unforgettable experiences, supported local artists, and built lasting relationships 
              that extend far beyond individual events. Join a network of conscious collaborators making real impact.
            </p>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                { metric: '50+', label: 'Venue Partners', description: 'Spaces transformed' },
                { metric: '25+', label: 'Brand Collaborations', description: 'Conscious alignments' },
                { metric: '100+', label: 'Artist Features', description: 'Talents showcased' }
              ].map((stat, i) => (
                <div key={i} className="text-center">
                  <div className="text-3xl font-black text-white mb-2">{stat.metric}</div>
                  <div className="text-white/80 font-bold text-sm mb-1">{stat.label}</div>
                  <div className="text-white/60 text-xs">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
