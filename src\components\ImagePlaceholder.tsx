import { ReactNode, CSSProperties } from 'react';

interface ImagePlaceholderProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  children?: ReactNode;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  alt?: string;
  style?: CSSProperties;
}

export default function ImagePlaceholder({
  width = '100%',
  height = '200px',
  className = '',
  children,
  aspectRatio,
  alt = 'Image placeholder',
  style: customStyle,
}: ImagePlaceholderProps) {
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]',
  };

  const aspectClass = aspectRatio ? aspectRatioClasses[aspectRatio] : '';

  const style = aspectRatio ? customStyle || {} : { width, height, ...customStyle };

  return (
    <div
      className={`image-placeholder ${aspectClass} ${className}`}
      style={style}
      role="img"
      aria-label={alt}
    >
      {children || (
        <svg
          className="w-8 h-8 text-current"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      )}
    </div>
  );
}
