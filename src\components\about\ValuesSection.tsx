'use client';

import { useState, useEffect, useRef } from 'react';
import HyperBadge from '../HyperBadge';
import { useAnimation } from '../../utils/AnimationManager';

export default function ValuesSection() {
  const [isVisible, setIsVisible] = useState(false);

  const [time, setTime] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  useAnimation('values-section', (currentTime) => {
    setTime(currentTime);
  }, 5);

  const coreValues = [
    {
      title: 'Consciousness',
      subtitle: 'Mindful Celebration',
      description: 'Every beat, every moment, every connection is intentional. We create spaces where awareness meets euphoria, where dancing becomes meditation in motion.',
      icon: '🧘',
      color: 'from-neon-pink to-neon-purple'
    },
    {
      title: 'Community',
      subtitle: 'Authentic Connection',
      description: 'We believe in the power of genuine human connection. Our events foster real relationships, breaking down barriers and building bridges between souls.',
      icon: '🤝',
      color: 'from-neon-cyan to-neon-blue'
    },
    {
      title: 'Creativity',
      subtitle: 'Boundless Expression',
      description: 'Art, music, and movement are the languages of the soul. We champion creative expression in all its forms, providing platforms for artists to shine.',
      icon: '🎨',
      color: 'from-neon-green to-neon-yellow'
    },
    {
      title: 'Transformation',
      subtitle: 'Personal Evolution',
      description: 'Dance floors are laboratories for personal growth. We create experiences that challenge, inspire, and transform participants on their journey of self-discovery.',
      icon: '🦋',
      color: 'from-neon-yellow to-neon-orange'
    },
    {
      title: 'Sustainability',
      subtitle: 'Conscious Impact',
      description: 'We honor our planet and community through sustainable practices. Every event considers environmental impact and contributes positively to our local ecosystem.',
      icon: '🌱',
      color: 'from-neon-green to-neon-cyan'
    },
    {
      title: 'Innovation',
      subtitle: 'Future Forward',
      description: 'We push boundaries and redefine what nightlife can be. Through technology, art, and visionary thinking, we create experiences that feel like glimpses of the future.',
      icon: '🚀',
      color: 'from-neon-purple to-neon-pink'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <section 
      ref={sectionRef}
      className="section-padding-large bg-black relative overflow-hidden"
    >
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Central Energy Core */}
        <div
          className="absolute w-[600px] h-[600px] rounded-full opacity-15 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.4) 0%, rgba(0, 255, 255, 0.2) 50%, transparent 100%)',
            left: '50%',
            top: '50%',
            transform: `translate(-50%, -50%) scale(${1 + Math.sin(time * 0.3) * 0.2}) rotate(${time * 2}deg)`,
          }}
        />

        {/* Orbiting Energy Blobs */}
        {coreValues.map((_, index) => (
          <div
            key={index}
            className="absolute w-32 h-32 rounded-full opacity-8 blur-2xl"
            style={{
              background: `radial-gradient(circle, ${coreValues[index].color.includes('pink') ? 'rgba(255, 0, 128, 0.3)' : 
                coreValues[index].color.includes('cyan') ? 'rgba(0, 255, 255, 0.3)' :
                coreValues[index].color.includes('green') ? 'rgba(0, 255, 65, 0.3)' :
                coreValues[index].color.includes('yellow') ? 'rgba(255, 255, 0, 0.3)' :
                coreValues[index].color.includes('purple') ? 'rgba(138, 43, 226, 0.3)' :
                'rgba(255, 69, 0, 0.3)'} 0%, transparent 70%)`,
              left: `${50 + Math.cos((time * 0.1) + (index * Math.PI * 2 / coreValues.length)) * 30}%`,
              top: `${50 + Math.sin((time * 0.1) + (index * Math.PI * 2 / coreValues.length)) * 30}%`,
              transform: `translate(-50%, -50%)`,
              transition: 'transform 0.5s ease-out',
            }}
          />
        ))}

        {/* Quantum Grid */}
        <div 
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 0, 128, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '100px 100px',
          }}
        />
      </div>

      <div className="content-container relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-8">
            <HyperBadge
              type="featured"
              text="OUR VALUES"
              size="lg"
            />
          </div>

          <h2 className="text-display-lg md:text-display-md font-black text-white leading-none tracking-tight mb-8">
            THE PRINCIPLES
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan via-neon-green to-neon-yellow animate-gradient-x">
              THAT GUIDE US
            </span>
          </h2>

          <p className="text-xl md:text-2xl font-medium text-white/70 leading-relaxed max-w-4xl mx-auto">
            These core values shape every decision, every event, and every connection we make. 
            They&apos;re not just words — they&apos;re the DNA of the collectiv experience.
          </p>
        </div>

        {/* Clean Media Object List */}
        <div className="space-y-6 mb-16 max-w-5xl mx-auto">
          {coreValues.map((value, index) => (
            <div
              key={index}
              className={`transition-all duration-700 delay-${index * 100} ${
                isVisible ? 'animate-slide-up-fade' : 'opacity-0'
              }`}
            >
              {/* Media Object Layout */}
              <div className="flex items-start space-x-6 p-6 md:p-8 glass-ultra rounded-3xl border border-white/10 hover:border-white/20 transition-all duration-300 group">

                {/* Left Column - Icon/Logo */}
                <div className="flex-shrink-0">
                  <div className={`w-16 h-16 md:w-20 md:h-20 rounded-2xl bg-gradient-to-r ${value.color} flex items-center justify-center text-2xl md:text-3xl group-hover:scale-110 transition-transform duration-300`}>
                    {value.icon}
                  </div>
                </div>

                {/* Right Column - Content */}
                <div className="flex-1 min-w-0">
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-2xl md:text-3xl font-black text-white leading-tight">{value.title}</h3>
                      <p className="text-sm font-semibold text-white/60 uppercase tracking-wide">{value.subtitle}</p>
                    </div>

                    <p className="text-white/70 leading-relaxed text-base md:text-lg">{value.description}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Values in Action */}
        <div className={`text-center transition-all duration-1000 delay-600 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="glass-ultra rounded-3xl p-12 border border-white/20 max-w-6xl mx-auto">
            <h3 className="text-3xl font-black text-white mb-6">
              VALUES IN
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink to-neon-cyan"> ACTION</span>
            </h3>
            <p className="text-white/70 text-lg mb-8 leading-relaxed max-w-4xl mx-auto">
              These aren&apos;t just ideals — they&apos;re lived experiences. Every collectiv event is a manifestation
              of these values, creating spaces where consciousness meets celebration, where community thrives,
              and where transformation happens naturally.
            </p>

            {/* Enhanced Action Grid - Mobile Optimized */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6 md:mb-8 px-4 md:px-0">
              {[
                { action: 'Mindful Curation', description: 'Every artist, every song, every moment carefully chosen', icon: '🎯', color: 'from-neon-pink to-neon-purple' },
                { action: 'Inclusive Spaces', description: 'Welcoming all souls regardless of background or identity', icon: '🌈', color: 'from-neon-cyan to-neon-blue' },
                { action: 'Sustainable Practices', description: 'Protecting our planet while we celebrate on it', icon: '🌱', color: 'from-neon-green to-neon-yellow' },
                { action: 'Artistic Excellence', description: 'Supporting and showcasing the finest creative talent', icon: '🎨', color: 'from-neon-yellow to-neon-orange' },
                { action: 'Community First', description: 'Building lasting connections beyond the dance floor', icon: '🤝', color: 'from-neon-orange to-neon-red' },
                { action: 'Future Forward', description: 'Pioneering the next evolution of conscious celebration', icon: '🚀', color: 'from-neon-purple to-neon-pink' }
              ].map((item, i) => (
                <div
                  key={i}
                  className="group p-4 md:p-6 glass rounded-xl md:rounded-2xl border border-white/10 hover:border-white/30 transition-all duration-300 hover:scale-105"
                >
                  <div className="space-y-2 md:space-y-3">
                    <div className={`w-10 h-10 md:w-12 md:h-12 rounded-lg md:rounded-xl bg-gradient-to-r ${item.color} flex items-center justify-center mx-auto text-base md:text-lg group-hover:scale-110 transition-transform duration-300`}>
                      {item.icon}
                    </div>
                    <div className="text-white font-bold text-sm md:text-base">{item.action}</div>
                    <div className="text-white/60 text-xs md:text-sm leading-relaxed">{item.description}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Impact Statement */}
            <div className="glass rounded-2xl p-6 border border-white/10 max-w-3xl mx-auto">
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-neon-green to-neon-cyan rounded-full flex items-center justify-center text-sm">✨</div>
                <div className="text-white font-bold">Real Impact, Real Change</div>
              </div>
              <p className="text-white/70 text-sm leading-relaxed">
                Over 95% of our community members report feeling more connected, inspired, and purposeful
                after attending collectiv events. This is the power of values-driven celebration.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
