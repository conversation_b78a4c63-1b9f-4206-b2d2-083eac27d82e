'use client';

import { useState, useEffect, useRef } from 'react';
import { useAnimation } from '../utils/AnimationManager';

interface DynamicBackgroundProps {
  variant?: 'hero' | 'section' | 'minimal';
  intensity?: number;
  className?: string;
}

function DynamicBackground({
  variant = 'section',
  intensity = 1,
  className = ''
}: DynamicBackgroundProps) {
  const [scrollY, setScrollY] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [time, setTime] = useState(0);
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
  const backgroundRef = useRef<HTMLDivElement>(null);

  useAnimation('dynamic-background', (currentTime) => {
    setTime(currentTime);
  }, 20);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth - 0.5) * 2,
        y: (e.clientY / window.innerHeight - 0.5) * 2,
      });
    };

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('mousemove', handleMouseMove, { passive: true });
    window.addEventListener('resize', handleResize);
    
    // Initial setup
    handleResize();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const getVariantConfig = () => {
    switch (variant) {
      case 'hero':
        return {
          blobCount: 6,
          blobSize: { min: 400, max: 800 },
          opacity: { min: 0.15, max: 0.25 },
          speed: 0.3,
          colors: [
            'rgba(255, 0, 128, 0.4)',
            'rgba(0, 255, 255, 0.3)',
            'rgba(0, 255, 65, 0.3)',
            'rgba(255, 255, 0, 0.3)',
            'rgba(138, 43, 226, 0.3)',
            'rgba(255, 69, 0, 0.3)'
          ]
        };
      case 'section':
        return {
          blobCount: 3,
          blobSize: { min: 300, max: 500 },
          opacity: { min: 0.08, max: 0.15 },
          speed: 0.2,
          colors: [
            'rgba(255, 0, 128, 0.3)',
            'rgba(0, 255, 255, 0.2)',
            'rgba(0, 255, 65, 0.2)'
          ]
        };
      case 'minimal':
        return {
          blobCount: 2,
          blobSize: { min: 200, max: 300 },
          opacity: { min: 0.05, max: 0.1 },
          speed: 0.1,
          colors: [
            'rgba(255, 0, 128, 0.2)',
            'rgba(0, 255, 255, 0.2)'
          ]
        };
      default:
        return {
          blobCount: 3,
          blobSize: { min: 300, max: 500 },
          opacity: { min: 0.08, max: 0.15 },
          speed: 0.2,
          colors: [
            'rgba(255, 0, 128, 0.3)',
            'rgba(0, 255, 255, 0.2)',
            'rgba(0, 255, 65, 0.2)'
          ]
        };
    }
  };

  const config = getVariantConfig();

  return (
    <div 
      ref={backgroundRef}
      className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}
    >
      {/* Morphing Energy Blobs */}
      {[...Array(config.blobCount)].map((_, index) => {
        const angle = (index * Math.PI * 2) / config.blobCount;
        const baseX = 50 + Math.cos(angle) * 30;
        const baseY = 50 + Math.sin(angle) * 30;
        
        return (
          <div
            key={index}
            className="absolute rounded-full blur-3xl"
            style={{
              width: `${config.blobSize.min + (config.blobSize.max - config.blobSize.min) * (0.5 + Math.sin(time * 0.1 + index) * 0.5)}px`,
              height: `${config.blobSize.min + (config.blobSize.max - config.blobSize.min) * (0.5 + Math.cos(time * 0.1 + index) * 0.5)}px`,
              background: `radial-gradient(circle, ${config.colors[index % config.colors.length]} 0%, transparent 70%)`,
              left: `${baseX + Math.sin(time * config.speed + index) * 20 + mousePosition.x * 5}%`,
              top: `${baseY + Math.cos(time * config.speed + index * 1.5) * 25 + mousePosition.y * 5 + (scrollY * 0.05)}%`,
              opacity: config.opacity.min + (config.opacity.max - config.opacity.min) * (0.5 + Math.sin(time * 0.5 + index) * 0.5),
              transform: `
                scale(${1 + Math.sin(time * 0.3 + index) * 0.2 * intensity})
                rotate(${time * (2 + index) * intensity}deg)
              `,
              transition: 'opacity 0.3s ease-out',
            }}
          />
        );
      })}

      {/* Quantum Grid Pattern */}
      <div 
        className="absolute inset-0"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255, 0, 128, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: `${80 + Math.sin(time * 0.1) * 20}px ${80 + Math.cos(time * 0.1) * 20}px`,
          opacity: 0.3 + Math.sin(time * 0.2) * 0.1,
          transform: `
            translateX(${mousePosition.x * 10 + Math.sin(time * 0.1) * 5}px)
            translateY(${mousePosition.y * 10 + Math.cos(time * 0.1) * 5}px)
          `,
        }}
      />

      {/* Floating Particles */}
      {variant === 'hero' && (
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: `${5 + (i * 4.5)}%`,
                top: `${10 + Math.sin(time * 0.3 + i) * 40}%`,
                opacity: 0.2 + Math.sin(time * 0.5 + i) * 0.2,
                transform: `
                  translateY(${Math.sin(time * 0.2 + i) * 30}px)
                  scale(${0.5 + Math.sin(time * 0.4 + i) * 0.5})
                `,
              }}
            />
          ))}
        </div>
      )}

      {/* Gradient Overlays */}
      <div 
        className="absolute inset-0"
        style={{
          background: `
            radial-gradient(circle at ${50 + mousePosition.x * 20}% ${50 + mousePosition.y * 20}%, 
              rgba(255, 0, 128, 0.1) 0%, 
              transparent 50%),
            radial-gradient(circle at ${30 + mousePosition.x * -15}% ${70 + mousePosition.y * -15}%, 
              rgba(0, 255, 255, 0.08) 0%, 
              transparent 50%)
          `,
          opacity: 0.6 + Math.sin(time * 0.1) * 0.2,
        }}
      />

      {/* Scroll-Responsive Overlay */}
      <div 
        className="absolute inset-0"
        style={{
          background: `linear-gradient(${180 + scrollY * 0.1}deg, 
            rgba(0, 0, 0, 0.1) 0%, 
            transparent 50%, 
            rgba(0, 0, 0, 0.05) 100%)`,
          opacity: Math.min(scrollY / 1000, 0.3),
        }}
      />

      {/* Interactive Light Rays */}
      {variant === 'hero' && (
        <div 
          className="absolute inset-0"
          style={{
            background: `
              conic-gradient(from ${time * 2}deg at ${50 + mousePosition.x * 10}% ${50 + mousePosition.y * 10}%, 
                transparent 0deg,
                rgba(255, 0, 128, 0.05) 45deg,
                transparent 90deg,
                rgba(0, 255, 255, 0.05) 135deg,
                transparent 180deg,
                rgba(0, 255, 65, 0.05) 225deg,
                transparent 270deg,
                rgba(255, 0, 128, 0.05) 315deg,
                transparent 360deg)
            `,
            opacity: 0.4 + Math.sin(time * 0.3) * 0.2,
          }}
        />
      )}

      {/* Performance Optimization Layer */}
      <div 
        className="absolute inset-0 bg-black/5"
        style={{
          mixBlendMode: 'multiply',
          opacity: Math.max(0, 1 - intensity),
        }}
      />
    </div>
  );
}

// Specialized background variants
export function HeroBackground() {
  return <DynamicBackground variant="hero" intensity={1.2} />;
}

export function SectionBackground() {
  return <DynamicBackground variant="section" intensity={0.8} />;
}

export function MinimalBackground() {
  return <DynamicBackground variant="minimal" intensity={0.5} />;
}

// Default export
export default DynamicBackground;
