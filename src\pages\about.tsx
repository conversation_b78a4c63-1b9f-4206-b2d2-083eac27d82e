import Layout from '../components/Layout';
import dynamic from 'next/dynamic';

// Load components dynamically with optimized loading strategy
const AboutHero = dynamic(() => import('../components/about/AboutHero'), {
  ssr: false,
  loading: () => <div className="min-h-screen bg-black" /> // Prevent layout shift
});

const OurStory = dynamic(() => import('../components/about/OurStory'), {
  ssr: false,
  loading: () => <div className="min-h-[50vh] bg-black" />
});

const TeamSection = dynamic(() => import('../components/about/TeamSection'), {
  ssr: false,
  loading: () => <div className="min-h-[50vh] bg-black" />
});

const ValuesSection = dynamic(() => import('../components/about/ValuesSection'), {
  ssr: false,
  loading: () => <div className="min-h-[50vh] bg-black" />
});

const ExperienceShowcase = dynamic(() => import('../components/about/ExperienceShowcase'), {
  ssr: false,
  loading: () => <div className="min-h-[50vh] bg-black" />
});

const StatsSection = dynamic(() => import('../components/about/StatsSection'), {
  ssr: false,
  loading: () => <div className="min-h-[50vh] bg-black" />
});

const PartnerWithUs = dynamic(() => import('../components/about/PartnerWithUs'), {
  ssr: false,
  loading: () => <div className="min-h-[50vh] bg-black" />
});

export default function About() {
  return (
    <Layout
      title="About Conscious Collectiv - Manifesting Dance Floor Euphoria in San Diego"
      description="Discover the story behind Conscious Collectiv - San Diego's premier conscious event collective. From sunset beach gatherings to underground warehouse parties, we're building a movement that values authenticity, mindfulness, and the transformative power of music and dance."
      url="https://consciouscollectiv.com/about"
      image="/images/about-og.jpg"
    >
      {/* Page Sections - Optimized Flow */}
      <AboutHero />
      <TeamSection />
      <OurStory />
      <ValuesSection />
      <ExperienceShowcase />
      <StatsSection />
      <PartnerWithUs />
    </Layout>
  );
}

// Disable static generation for this page to avoid SSR issues
export async function getServerSideProps() {
  return {
    props: {},
  };
}
