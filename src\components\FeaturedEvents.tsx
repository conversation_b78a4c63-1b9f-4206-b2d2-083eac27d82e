import { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import EventCard from './EventCard';
import HyperBadge from './HyperBadge';
import HyperButton from './HyperButton';

// Mock data for featured events
const featuredEvents = [
  {
    id: '1',
    title: 'Sunset Beach Vibes',
    date: '2025-07-15',
    location: 'Manhattan Beach, CA',
  },
  {
    id: '2',
    title: 'Conscious Pool Party',
    date: '2025-07-22',
    location: 'Venice, CA',
  },
  {
    id: '3',
    title: 'Full Moon Gathering',
    date: '2025-07-29',
    location: 'Malibu, CA',
  },
  {
    id: '4',
    title: 'Rooftop Connections',
    date: '2025-08-05',
    location: 'West Hollywood, CA',
  },
  {
    id: '5',
    title: 'Desert Retreat',
    date: '2025-08-12',
    location: 'Joshua Tree, CA',
  },
];

export default function FeaturedEvents() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [time, setTime] = useState(0);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.3 }
    );

    const section = document.getElementById('events');
    if (section) {
      observer.observe(section);
    }

    // Optimized animation timer using requestAnimationFrame
    let animationId: number;
    let startTime = Date.now();

    const animate = () => {
      if (document.hidden) return; // Pause when tab is hidden

      const elapsed = (Date.now() - startTime) / 1000;
      setTime(elapsed);
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      observer.disconnect();
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -320, // Card width + gap
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 320, // Card width + gap
        behavior: 'smooth',
      });
    }
  };

  return (
    <section id="events" className="section-padding-large relative bg-black overflow-hidden">
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Morphing Energy Fields */}
        <div
          className="absolute w-96 h-96 rounded-full opacity-15 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.6) 0%, transparent 70%)',
            left: `${30 + Math.sin(time * 0.5) * 20}%`,
            top: `${20 + Math.cos(time * 0.3) * 15}%`,
            transform: `scale(${1 + Math.sin(time * 0.4) * 0.3}) rotate(${time * 20}deg)`,
          }}
        />
        <div
          className="absolute w-80 h-80 rounded-full opacity-12 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, transparent 70%)',
            right: `${25 + Math.cos(time * 0.7) * 18}%`,
            bottom: `${30 + Math.sin(time * 0.6) * 12}%`,
            transform: `scale(${1 + Math.cos(time * 0.5) * 0.2}) rotate(${-time * 15}deg)`,
          }}
        />

        {/* Neural Network Grid */}
        <div className="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="neural-events" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
                <circle cx="100" cy="100" r="3" fill="rgba(255, 255, 255, 0.4)" />
                <line x1="100" y1="100" x2="200" y2="0" stroke="rgba(255, 255, 255, 0.2)" strokeWidth="1" />
                <line x1="100" y1="100" x2="0" y2="200" stroke="rgba(0, 255, 255, 0.2)" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#neural-events)" />
          </svg>
        </div>
      </div>

      <div className="content-container relative z-10">
        {/* Enhanced Section Header */}
        <div className={`text-center mb-24 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-12">
            <HyperBadge
              type="featured"
              text="UPCOMING EVENTS"
              size="lg"
            />
          </div>

          <h2 className="mb-12 text-display-lg md:text-display-md font-black text-white leading-none tracking-tight">
            EXPERIENCE THE
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-purple to-neon-cyan animate-gradient-x">
              COLLECTIVE
            </span>
          </h2>

          <p className="text-xl text-white/80 max-w-4xl mx-auto leading-tight font-medium">
            Dance floor euphoria awaits at our carefully curated events across San Diego&apos;s most iconic venues
          </p>
        </div>

        {/* Ultra-Modern Carousel Container */}
        <div className="relative perspective-1000">
          {/* Holographic Navigation Buttons */}
          <div className="hidden md:block">
            <button
              onClick={scrollLeft}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-6 z-20 w-14 h-14 glass rounded-full flex items-center justify-center transition-all duration-500 border border-white/20 hover:border-white/40 group hover:scale-110"
              aria-label="Previous events"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-neon-pink/20 to-neon-cyan/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <svg
                className="w-6 h-6 text-white relative z-10 group-hover:text-neon-cyan transition-colors duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <button
              onClick={scrollRight}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-6 z-20 w-14 h-14 glass rounded-full flex items-center justify-center transition-all duration-500 border border-white/20 hover:border-white/40 group hover:scale-110"
              aria-label="Next events"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-neon-cyan/20 to-neon-pink/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <svg
                className="w-6 h-6 text-white relative z-10 group-hover:text-neon-pink transition-colors duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>

          {/* 3D Enhanced Carousel */}
          <div
            ref={scrollContainerRef}
            className="carousel-container gap-8 pb-6 transform-3d"
            style={{ scrollSnapType: 'x mandatory' }}
          >
            {featuredEvents.map((event, index) => (
              <div
                key={event.id}
                className="carousel-item w-80 flex-shrink-0"
                style={{
                  scrollSnapAlign: 'start',
                }}
              >
                <EventCard {...event} />
              </div>
            ))}
          </div>

          {/* Enhanced Mobile Indicator */}
          <div className="md:hidden text-center mt-6">
            <div className="inline-flex items-center px-4 py-2 glass rounded-full">
              <span className="text-sm text-white/70 mr-2">Swipe to explore</span>
              <div className="w-4 h-4 text-neon-cyan animate-bounce">→</div>
            </div>
          </div>
        </div>

        {/* Ultra-Modern CTA Button */}
        <div className={`text-center mt-20 transition-all duration-1000 delay-300 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <Link href="/events">
            <HyperButton
              variant="quantum"
              size="lg"
              icon="🎉"
            >
              View All Events
            </HyperButton>
          </Link>
        </div>
      </div>
    </section>
  );
}
