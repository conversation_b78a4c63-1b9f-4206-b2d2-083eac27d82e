'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import HyperBadge from '../HyperBadge';
import { useAnimation } from '../../utils/AnimationManager';

export default function StatsSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [animatedStats, setAnimatedStats] = useState<{[key: string]: number}>({});
  const [time, setTime] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  useAnimation('stats-section', (currentTime) => {
    setTime(currentTime);
  }, 7);

  const stats = useMemo(() => [
    {
      id: 'events',
      value: 127,
      label: 'Events Hosted',
      description: 'Conscious celebrations across San Diego',
      icon: '🎉',
      color: 'from-neon-pink to-neon-purple',
      suffix: '+'
    },
    {
      id: 'community',
      value: 8500,
      label: 'Community Members',
      description: 'Souls connected through music and movement',
      icon: '👥',
      color: 'from-neon-cyan to-neon-blue',
      suffix: '+'
    },
    {
      id: 'artists',
      value: 89,
      label: 'Artists Featured',
      description: 'Local and international talent showcased',
      icon: '🎨',
      color: 'from-neon-green to-neon-yellow',
      suffix: ''
    },
    {
      id: 'venues',
      value: 34,
      label: 'Unique Venues',
      description: 'Spaces transformed into sonic sanctuaries',
      icon: '🏛️',
      color: 'from-neon-yellow to-neon-orange',
      suffix: ''
    },
    {
      id: 'hours',
      value: 2400,
      label: 'Hours of Music',
      description: 'Beats that moved bodies and souls',
      icon: '🎵',
      color: 'from-neon-purple to-neon-pink',
      suffix: '+'
    },
    {
      id: 'impact',
      value: 95,
      label: 'Positive Impact',
      description: 'Community members reporting life transformation',
      icon: '✨',
      color: 'from-neon-orange to-neon-red',
      suffix: '%'
    }
  ], []);

  const achievements = [
    {
      title: 'San Diego\'s #1 Conscious Event Collective',
      description: 'Recognized by local media and community',
      icon: '🏆',
      year: '2024'
    },
    {
      title: 'Zero Waste Events Initiative',
      description: 'Leading sustainable event practices',
      icon: '🌱',
      year: '2024'
    },
    {
      title: 'Artist Development Program',
      description: 'Supporting emerging local talent',
      icon: '🚀',
      year: '2024'
    },
    {
      title: 'Community Wellness Integration',
      description: 'Mental health and mindfulness focus',
      icon: '🧘',
      year: '2024'
    }
  ];

  // Animate counters when visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          
          // Animate each stat counter
          stats.forEach((stat) => {
            let current = 0;
            const increment = stat.value / 60; // 60 frames for smooth animation
            const timer = setInterval(() => {
              current += increment;
              if (current >= stat.value) {
                current = stat.value;
                clearInterval(timer);
              }
              setAnimatedStats(prev => ({
                ...prev,
                [stat.id]: Math.floor(current)
              }));
            }, 50);
          });
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [isVisible, stats]);

  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <section 
      ref={sectionRef}
      className="section-padding-large bg-black relative overflow-hidden"
    >
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Central Pulsing Core */}
        <div
          className="absolute w-[800px] h-[800px] rounded-full opacity-10 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.4) 0%, rgba(0, 255, 255, 0.2) 50%, transparent 100%)',
            left: '50%',
            top: '50%',
            transform: `translate(-50%, -50%) scale(${1 + Math.sin(time * 0.5) * 0.3}) rotate(${time * 1}deg)`,
          }}
        />

        {/* Orbiting Data Blobs */}
        {stats.map((_, index) => (
          <div
            key={index}
            className="absolute w-24 h-24 rounded-full opacity-15 blur-2xl"
            style={{
              background: `radial-gradient(circle, ${
                index % 6 === 0 ? 'rgba(255, 0, 128, 0.4)' :
                index % 6 === 1 ? 'rgba(0, 255, 255, 0.4)' :
                index % 6 === 2 ? 'rgba(0, 255, 65, 0.4)' :
                index % 6 === 3 ? 'rgba(255, 255, 0, 0.4)' :
                index % 6 === 4 ? 'rgba(138, 43, 226, 0.4)' :
                'rgba(255, 69, 0, 0.4)'
              } 0%, transparent 70%)`,
              left: `${50 + Math.cos((time * 0.2) + (index * Math.PI * 2 / stats.length)) * 35}%`,
              top: `${50 + Math.sin((time * 0.2) + (index * Math.PI * 2 / stats.length)) * 35}%`,
              transform: `translate(-50%, -50%) scale(${1 + Math.sin(time * 0.8 + index) * 0.3})`,
            }}
          />
        ))}

        {/* Data Stream Lines */}
        <div 
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 0, 128, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '120px 120px',
            transform: `translateX(${Math.sin(time * 0.1) * 20}px) translateY(${Math.cos(time * 0.1) * 20}px)`,
          }}
        />
      </div>

      <div className="content-container relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-8">
            <HyperBadge
              type="live"
              text="IMPACT METRICS"
              size="lg"
            />
          </div>

          <h2 className="text-display-lg md:text-display-md font-black text-white leading-none tracking-tight mb-8">
            BY THE
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan via-neon-green to-neon-yellow animate-gradient-x">
              NUMBERS
            </span>
          </h2>

          <p className="text-xl md:text-2xl font-medium text-white/70 leading-relaxed max-w-4xl mx-auto">
            Since our inception, collectiv has grown from a vision into a movement. 
            These numbers represent real connections, transformative experiences, and a community united by conscious celebration.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div
              key={stat.id}
              className={`relative group transition-all duration-700 delay-${index * 100} ${
                isVisible ? 'animate-slide-up-fade' : 'opacity-0'
              }`}
            >
              {/* Stat Card */}
              <div className="relative p-8 rounded-3xl border border-white/10 glass-ultra hover:scale-105 transition-all duration-500">
                
                {/* Icon */}
                <div className="mb-6">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center text-2xl mx-auto group-hover:scale-110 group-hover:rotate-12 transition-transform duration-500`}>
                    {stat.icon}
                  </div>
                </div>

                {/* Number */}
                <div className="text-center mb-4">
                  <div className="text-4xl md:text-5xl font-black text-white mb-2">
                    {formatNumber(animatedStats[stat.id] || 0)}{stat.suffix}
                  </div>
                  <h3 className="text-lg font-bold text-white/90">{stat.label}</h3>
                </div>

                {/* Description */}
                <p className="text-white/60 text-sm text-center leading-relaxed">{stat.description}</p>

                {/* Animated Progress Bar */}
                <div className="mt-6">
                  <div className="w-full h-1 bg-white/10 rounded-full overflow-hidden">
                    <div 
                      className={`h-full bg-gradient-to-r ${stat.color} transition-all duration-2000 ease-out`}
                      style={{
                        width: isVisible ? '100%' : '0%',
                        transitionDelay: `${index * 200}ms`
                      }}
                    />
                  </div>
                </div>

                {/* Hover Particles */}
                <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {[...Array(8)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                      style={{
                        left: `${20 + Math.random() * 60}%`,
                        top: `${20 + Math.random() * 60}%`,
                        animationDelay: `${Math.random() * 0.5}s`,
                      }}
                    />
                  ))}
                </div>

                {/* Quantum Border Effect */}
                <div 
                  className="absolute inset-0 rounded-3xl border-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    borderImage: `linear-gradient(135deg, ${stat.color.replace('from-', '').replace('to-', ', ')}) 1`,
                    boxShadow: `0 0 30px rgba(255, 255, 255, 0.1)`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Achievements Section */}
        <div className={`transition-all duration-1000 delay-800 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="text-center mb-12">
            <h3 className="text-3xl font-black text-white mb-4">
              RECENT
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink to-neon-cyan"> ACHIEVEMENTS</span>
            </h3>
            <p className="text-white/70 text-lg">Milestones that mark our journey</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => (
              <div
                key={index}
                className="relative group"
              >
                <div className="glass rounded-2xl p-6 border border-white/10 hover:glass-ultra hover:scale-105 transition-all duration-500">
                  {/* Achievement Icon */}
                  <div className="text-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-neon-yellow to-neon-orange rounded-xl flex items-center justify-center text-xl mx-auto group-hover:scale-110 transition-transform duration-300">
                      {achievement.icon}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="text-center space-y-3">
                    <div className="text-xs font-bold text-neon-cyan uppercase tracking-wide">{achievement.year}</div>
                    <h4 className="text-sm font-black text-white leading-tight">{achievement.title}</h4>
                    <p className="text-white/60 text-xs leading-relaxed">{achievement.description}</p>
                  </div>

                  {/* Hover Glow */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Future Goals */}
        <div className={`text-center mt-20 transition-all duration-1000 delay-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="glass-ultra rounded-3xl p-12 border border-white/20 max-w-4xl mx-auto">
            <h3 className="text-3xl font-black text-white mb-6">
              LOOKING
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-green to-neon-cyan"> AHEAD</span>
            </h3>
            <p className="text-white/70 text-lg mb-8 leading-relaxed">
              These numbers are just the beginning. Our vision extends far beyond metrics — we&apos;re building a global movement 
              that transforms how people connect, celebrate, and grow together through conscious experiences.
            </p>
            <div className="flex flex-wrap gap-4 justify-center">
              {['10K Community Members', 'International Expansion', 'Artist Residency Program', 'Wellness Integration', 'Tech Innovation'].map((goal, i) => (
                <span 
                  key={i}
                  className="px-6 py-3 bg-gradient-to-r from-white/10 to-white/5 rounded-full text-sm font-bold text-white border border-white/20 hover:scale-105 transition-transform duration-300"
                >
                  {goal}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
