import { GetStaticProps, GetStaticPaths } from 'next';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout';
import ImagePlaceholder from '../../components/ImagePlaceholder';

interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  price: string;
  capacity: number;
  category: string;
}

interface EventPageProps {
  event: Event;
}

// Mock event data
const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Sunset Beach Vibes',
    date: '2025-07-15',
    time: '6:00 PM - 10:00 PM',
    location: 'Manhattan Beach, CA',
    description: 'Join us for an unforgettable sunset experience on the beautiful Manhattan Beach. Connect with like-minded souls as we watch the sun dip below the horizon, enjoy conscious conversations, and create lasting memories together.',
    price: '$25',
    capacity: 50,
    category: 'Beach',
  },
  {
    id: '2',
    title: 'Conscious Pool Party',
    date: '2025-07-22',
    time: '2:00 PM - 8:00 PM',
    location: 'Venice, CA',
    description: 'Dive into a day of conscious connection at our exclusive pool party. Enjoy refreshing vibes, meaningful conversations, and the perfect blend of relaxation and celebration in the heart of Venice.',
    price: '$35',
    capacity: 75,
    category: 'Pool',
  },
  // Add more mock events as needed
];

export default function EventPage({ event }: EventPageProps) {
  const router = useRouter();

  if (router.isFallback) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-3 h-3 bg-neon-cyan rounded-full animate-pulse"></div>
              <div className="w-3 h-3 bg-neon-pink rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
            <p>Loading event details...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!event) {
    return (
      <Layout title="Event Not Found">
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Event Not Found</h1>
            <p className="text-text-secondary mb-8">The event you&apos;re looking for doesn&apos;t exist.</p>
            <button 
              onClick={() => router.push('/')}
              className="btn-gradient"
            >
              Back to Home
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  const eventDate = new Date(event.date);
  const formattedDate = eventDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <Layout
      title={`${event.title} | Conscious Collectiv`}
      description={event.description}
      type="article"
    >
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative h-96 overflow-hidden">
          <ImagePlaceholder
            className="absolute inset-0 w-full h-full"
            alt={`${event.title} event image`}
          />
          <div className="absolute inset-0 bg-gradient-overlay" />
          <div className="relative z-10 content-container h-full flex items-end pb-12">
            <div className="text-white">
              <div className="inline-flex items-center px-3 py-1 bg-white bg-opacity-20 rounded-pill text-sm font-medium mb-4">
                {event.category}
              </div>
              <h1 className="text-white mb-2">{event.title}</h1>
              <p className="text-lg opacity-90">{formattedDate}</p>
            </div>
          </div>
        </section>

        {/* Event Details */}
        <section className="section-padding">
          <div className="content-container">
            <div className="grid lg:grid-cols-3 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-8">
                <div>
                  <h2 className="mb-4">About This Event</h2>
                  <p className="text-text-secondary leading-relaxed">
                    {event.description}
                  </p>
                </div>

                {/* Event Gallery Placeholder */}
                <div>
                  <h3 className="mb-4">Event Gallery</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                      <ImagePlaceholder
                        key={i}
                        aspectRatio="square"
                        className="w-full"
                        alt={`${event.title} gallery image ${i}`}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Event Info Card */}
                <div className="card sticky top-8">
                  <h3 className="mb-6">Event Details</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <svg className="w-5 h-5 text-gradient-start mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h6m-6 0l-1 1v4a2 2 0 01-2 2H6a2 2 0 01-2-2V8l-1-1m1 0h6m-6 0l1-1" />
                      </svg>
                      <div>
                        <p className="font-medium">Date & Time</p>
                        <p className="text-text-secondary text-sm">{formattedDate}</p>
                        <p className="text-text-secondary text-sm">{event.time}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <svg className="w-5 h-5 text-gradient-start mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <div>
                        <p className="font-medium">Location</p>
                        <p className="text-text-secondary text-sm">{event.location}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <svg className="w-5 h-5 text-gradient-start mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                      <div>
                        <p className="font-medium">Price</p>
                        <p className="text-text-secondary text-sm">{event.price}</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <svg className="w-5 h-5 text-gradient-start mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      <div>
                        <p className="font-medium">Capacity</p>
                        <p className="text-text-secondary text-sm">{event.capacity} people</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8 space-y-3">
                    <button className="btn-gradient w-full">
                      Get Tickets
                    </button>
                    <button className="btn-outline w-full">
                      Add to Calendar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}

export const getStaticPaths: GetStaticPaths = async () => {
  const paths = mockEvents.map((event) => ({
    params: { id: event.id },
  }));

  return {
    paths,
    fallback: true,
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const event = mockEvents.find((e) => e.id === params?.id);

  if (!event) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      event,
    },
    revalidate: 60, // Revalidate every minute
  };
};
