'use client';

import { useState, useEffect, useRef } from 'react';
import HyperBadge from '../HyperBadge';
import ImagePlaceholder from '../ImagePlaceholder';
import VideoPlaceholder from '../VideoPlaceholder';
import { useAnimation } from '../../utils/AnimationManager';

export default function ExperienceShowcase() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeExperience, setActiveExperience] = useState(0);
  const [time, setTime] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  useAnimation('experience-showcase', (currentTime) => {
    setTime(currentTime);
  }, 6);

  const experiences = [
    {
      title: 'Sunset Sessions',
      subtitle: 'Beach Euphoria',
      description: 'Golden hour gatherings where ocean waves meet electronic beats. Barefoot dancing on sand as the sun melts into the Pacific horizon.',
      features: ['Oceanfront Venues', 'Sunset Timing', 'Chill Vibes', 'Natural Beauty'],
      vibe: 'Serene',
      color: 'from-orange-400 to-pink-500',
      icon: '🌅',
      type: 'video'
    },
    {
      title: 'Underground Warehouse',
      subtitle: 'Raw Energy',
      description: 'Industrial spaces transformed into sonic sanctuaries. Concrete walls pulse with bass while lasers paint geometric dreams in the darkness.',
      features: ['Industrial Venues', 'Cutting-Edge Sound', 'Immersive Visuals', 'Underground Culture'],
      vibe: 'Intense',
      color: 'from-purple-500 to-cyan-400',
      icon: '🏭',
      type: 'image'
    },
    {
      title: 'Rooftop Revelry',
      subtitle: 'City Lights',
      description: 'Dance above the city skyline with stars as your ceiling. Urban energy meets cosmic consciousness in elevated celebration.',
      features: ['Skyline Views', 'Open Air', 'City Energy', 'Elevated Experience'],
      vibe: 'Electric',
      color: 'from-blue-400 to-purple-600',
      icon: '🏙️',
      type: 'image'
    },
    {
      title: 'Forest Gatherings',
      subtitle: 'Nature\'s Cathedral',
      description: 'Ancient trees witness modern rituals as electronic music harmonizes with natural acoustics. Mystical experiences in sacred groves.',
      features: ['Natural Venues', 'Eco-Conscious', 'Mystical Atmosphere', 'Organic Integration'],
      vibe: 'Mystical',
      color: 'from-green-400 to-emerald-600',
      icon: '🌲',
      type: 'video'
    },
    {
      title: 'Art Gallery Nights',
      subtitle: 'Creative Fusion',
      description: 'Where visual art meets sonic art. Gallery walls become canvases for projection mapping while curated music enhances artistic expression.',
      features: ['Art Integration', 'Cultural Venues', 'Creative Collaboration', 'Intimate Settings'],
      vibe: 'Sophisticated',
      color: 'from-pink-400 to-rose-600',
      icon: '🎨',
      type: 'image'
    },
    {
      title: 'Boat Parties',
      subtitle: 'Floating Paradise',
      description: 'San Diego Bay becomes our dance floor as we cruise past city lights. Ocean breeze, endless horizon, and beats that flow like waves.',
      features: ['Water Venues', 'Mobile Experience', 'Scenic Routes', 'Unique Perspective'],
      vibe: 'Adventurous',
      color: 'from-cyan-400 to-blue-600',
      icon: '⛵',
      type: 'video'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    // Auto-rotate experiences
    const experienceTimer = setInterval(() => {
      setActiveExperience(prev => (prev + 1) % experiences.length);
    }, 6000);

    return () => {
      observer.disconnect();
      clearInterval(experienceTimer);
    };
  }, [experiences.length]);

  return (
    <section 
      ref={sectionRef}
      className="section-padding-large bg-black relative overflow-hidden"
    >
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Morphing Experience Blobs */}
        <div
          className="absolute w-[700px] h-[700px] rounded-full opacity-12 blur-3xl"
          style={{
            background: `radial-gradient(circle, ${
              experiences[activeExperience].color.includes('orange') ? 'rgba(251, 146, 60, 0.4)' :
              experiences[activeExperience].color.includes('purple') ? 'rgba(168, 85, 247, 0.4)' :
              experiences[activeExperience].color.includes('blue') ? 'rgba(59, 130, 246, 0.4)' :
              experiences[activeExperience].color.includes('green') ? 'rgba(34, 197, 94, 0.4)' :
              experiences[activeExperience].color.includes('pink') ? 'rgba(236, 72, 153, 0.4)' :
              'rgba(6, 182, 212, 0.4)'
            } 0%, transparent 70%)`,
            left: `${20 + Math.sin(time * 0.2) * 25}%`,
            top: `${30 + Math.cos(time * 0.25) * 30}%`,
            transform: `scale(${1 + Math.sin(time * 0.3) * 0.3}) rotate(${time * 4}deg)`,
            transition: 'background 1s ease-out',
          }}
        />

        {/* Secondary Energy Blob */}
        <div
          className="absolute w-[500px] h-[500px] rounded-full opacity-8 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%)',
            right: `${15 + Math.cos(time * 0.18) * 20}%`,
            bottom: `${25 + Math.sin(time * 0.22) * 25}%`,
            transform: `scale(${1 + Math.cos(time * 0.25) * 0.2}) rotate(${-time * 3}deg)`,
          }}
        />

        {/* Floating Experience Particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(25)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-20"
              style={{
                left: `${5 + (i * 3.8)}%`,
                top: `${10 + Math.sin(time * 0.4 + i) * 50}%`,
                transform: `translateY(${Math.sin(time * 0.2 + i) * 40}px)`,
                animationDelay: `${i * 0.08}s`,
              }}
            />
          ))}
        </div>
      </div>

      <div className="content-container relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-8">
            <HyperBadge
              type="trending"
              text="EXPERIENCE TYPES"
              size="lg"
            />
          </div>

          <h2 className="text-display-lg md:text-display-md font-black text-white leading-none tracking-tight mb-8">
            DIVERSE
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-orange via-neon-pink to-neon-cyan animate-gradient-x">
              EXPERIENCES
            </span>
          </h2>

          <p className="text-xl md:text-2xl font-medium text-white/70 leading-relaxed max-w-4xl mx-auto">
            From intimate gallery nights to massive beach gatherings, each collectiv experience is uniquely crafted 
            to match the energy, setting, and consciousness of the moment.
          </p>
        </div>

        {/* Main Experience Showcase */}
        <div className="grid lg:grid-cols-2 gap-16 mb-20">
          {/* Left - Experience Selector */}
          <div className={`space-y-6 transition-all duration-1000 delay-200 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            {experiences.map((experience, index) => (
              <div
                key={index}
                className={`relative p-6 rounded-2xl border border-white/10 cursor-pointer transition-all duration-500 ${
                  activeExperience === index 
                    ? 'glass-ultra scale-105' 
                    : 'glass hover:glass-ultra'
                }`}
                onClick={() => setActiveExperience(index)}
              >
                {/* Active Indicator */}
                {activeExperience === index && (
                  <div className="absolute -left-3 top-1/2 transform -translate-y-1/2">
                    <div className={`w-6 h-6 rounded-full bg-gradient-to-r ${experience.color} animate-pulse-glow`}>
                      <div className="w-full h-full bg-white rounded-full scale-50"></div>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  {/* Icon */}
                  <div className={`w-14 h-14 rounded-xl bg-gradient-to-r ${experience.color} flex items-center justify-center text-xl transition-transform duration-300 ${
                    activeExperience === index ? 'scale-110' : ''
                  }`}>
                    {experience.icon}
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-black text-white">{experience.title}</h3>
                      <HyperBadge type="new" text={experience.vibe} size="sm" />
                    </div>
                    <p className="text-sm font-medium text-white/60 mb-2">{experience.subtitle}</p>
                    <p className="text-white/70 text-sm leading-relaxed">{experience.description}</p>
                  </div>
                </div>

                {/* Features */}
                <div className="mt-4 flex flex-wrap gap-2">
                  {experience.features.map((feature, i) => (
                    <span 
                      key={i}
                      className="px-3 py-1 bg-white/5 rounded-full text-xs font-medium text-white/70 border border-white/10"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* Hover Particles */}
                {activeExperience === index && (
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(10)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                        style={{
                          left: `${15 + Math.random() * 70}%`,
                          top: `${15 + Math.random() * 70}%`,
                          animationDelay: `${Math.random() * 0.8}s`,
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right - Visual Showcase */}
          <div className={`relative transition-all duration-1000 delay-400 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="relative perspective-1000">
              <div className="relative transform-3d group">
                {/* Main Visual */}
                {experiences[activeExperience].type === 'video' ? (
                  <VideoPlaceholder
                    className="w-full rounded-3xl shadow-2xl group-hover:scale-105 transition-transform duration-700"
                    showPlayButton={true}
                    overlay={true}
                    aspectRatio="video"
                    alt={`${experiences[activeExperience].title} experience`}
                  />
                ) : (
                  <ImagePlaceholder
                    aspectRatio="video"
                    className="w-full rounded-3xl shadow-2xl group-hover:scale-105 transition-transform duration-700"
                    alt={`${experiences[activeExperience].title} experience`}
                  />
                )}

                {/* Holographic Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${experiences[activeExperience].color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-3xl`} />

                {/* Floating Info Widget */}
                <div className="absolute -top-6 -right-6 glass-ultra rounded-2xl p-4 border border-white/20 animate-float-slow">
                  <div className="text-center space-y-2">
                    <div className={`w-10 h-10 rounded-xl bg-gradient-to-r ${experiences[activeExperience].color} flex items-center justify-center mx-auto`}>
                      <span className="text-black font-black text-sm">{experiences[activeExperience].icon}</span>
                    </div>
                    <div className="space-y-1">
                      <div className="text-white font-bold text-xs">{experiences[activeExperience].vibe.toUpperCase()}</div>
                      <div className="text-white/70 text-xs">{experiences[activeExperience].subtitle}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Experience Stats */}
            <div className="mt-8 grid grid-cols-2 gap-4">
              <div className="glass rounded-2xl p-6 text-center border border-white/10">
                <div className="text-2xl font-black text-white mb-2">50+</div>
                <div className="text-white/60 text-sm font-medium">Unique Venues</div>
              </div>
              <div className="glass rounded-2xl p-6 text-center border border-white/10">
                <div className="text-2xl font-black text-white mb-2">6</div>
                <div className="text-white/60 text-sm font-medium">Experience Types</div>
              </div>
            </div>
          </div>
        </div>

        {/* Experience Gallery */}
        <div className={`transition-all duration-1000 delay-600 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="text-center mb-12">
            <h3 className="text-3xl font-black text-white mb-4">
              EXPERIENCE
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink to-neon-cyan"> GALLERY</span>
            </h3>
            <p className="text-white/70 text-lg">A glimpse into the diverse worlds we create</p>
          </div>

          <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-4">
            {experiences.map((experience, index) => (
              <div
                key={index}
                className="relative group cursor-pointer"
                onClick={() => setActiveExperience(index)}
              >
                <ImagePlaceholder
                  aspectRatio="square"
                  className="w-full rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                  alt={`${experience.title} preview`}
                />
                <div className={`absolute inset-0 bg-gradient-to-br ${experience.color} opacity-0 group-hover:opacity-30 transition-opacity duration-300 rounded-xl`} />
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="text-3xl">{experience.icon}</div>
                </div>
                <div className="absolute bottom-2 left-2">
                  <HyperBadge type="new" text={experience.vibe} size="sm" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
