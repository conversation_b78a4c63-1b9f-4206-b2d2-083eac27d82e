/**
 * Centralized Animation Manager
 * Provides a single requestAnimationFrame loop for all components
 * Handles visibility API, performance optimization, and time synchronization
 */

import React from 'react';

interface AnimationCallback {
  id: string;
  callback: (time: number, deltaTime: number) => void;
  priority: number; // Lower numbers = higher priority
}

class CentralizedAnimationManager {
  private callbacks: Map<string, AnimationCallback> = new Map();
  private animationFrameId: number | null = null;
  private startTime: number = Date.now();
  private lastTime: number = 0;
  private isVisible: boolean = true;
  private isRunning: boolean = false;
  private hasInitialized: boolean = false;
  private boundStart: () => void;
  private boundHandleVisibilityChange: () => void;

  constructor() {
    this.boundHandleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.boundStart = this.start.bind(this);
    this.animate = this.animate.bind(this);

    // Only run in browser environment
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      // Prevent double initialization in React Strict Mode
      if (this.hasInitialized) return;
      this.hasInitialized = true;

      // Listen for visibility changes
      document.addEventListener('visibilitychange', this.boundHandleVisibilityChange);

      // Listen for performance-ready event with proper cleanup reference
      window.addEventListener('performance-ready', this.boundStart, { once: true });

      // Fallback timer in case performance-ready doesn't fire
      setTimeout(() => {
        if (!this.isRunning) {
          this.start();
        }
      }, 200); // Increased to avoid race conditions
    }
  }

  private handleVisibilityChange(): void {
    if (typeof document !== 'undefined') {
      this.isVisible = !document.hidden;

      if (this.isVisible && !this.isRunning) {
        this.start();
      } else if (!this.isVisible && this.isRunning) {
        this.pause();
      }
    }
  }

  private animate(): void {
    if (!this.isVisible) {
      this.animationFrameId = requestAnimationFrame(this.animate);
      return;
    }

    const currentTime = Date.now();
    const elapsedTime = (currentTime - this.startTime) / 1000;
    const deltaTime = (currentTime - this.lastTime) / 1000;
    this.lastTime = currentTime;

    // Sort callbacks by priority and execute
    const sortedCallbacks = Array.from(this.callbacks.values())
      .sort((a, b) => a.priority - b.priority);

    for (const { callback } of sortedCallbacks) {
      try {
        callback(elapsedTime, deltaTime);
      } catch (error) {
        console.warn('Animation callback error:', error);
      }
    }

    this.animationFrameId = requestAnimationFrame(this.animate);
  }

  public start(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.lastTime = Date.now();
    this.animationFrameId = requestAnimationFrame(this.animate);
  }

  public pause(): void {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  public register(
    id: string, 
    callback: (time: number, deltaTime: number) => void, 
    priority: number = 0
  ): void {
    this.callbacks.set(id, { id, callback, priority });
    
    // Start animation loop if this is the first callback
    if (this.callbacks.size === 1 && this.isVisible) {
      this.start();
    }
  }

  public unregister(id: string): void {
    this.callbacks.delete(id);
    
    // Stop animation loop if no callbacks remain
    if (this.callbacks.size === 0) {
      this.pause();
    }
  }

  public getTime(): number {
    return (Date.now() - this.startTime) / 1000;
  }

  public getCallbackCount(): number {
    return this.callbacks.size;
  }

  public destroy(): void {
    this.pause();
    this.callbacks.clear();

    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', this.boundHandleVisibilityChange);
      window.removeEventListener('performance-ready', this.boundStart);
    }
  }
}

// Create singleton instance
export const animationManager = new CentralizedAnimationManager();

// React hook for easy component integration
export function useAnimation(
  id: string,
  callback: (time: number, deltaTime: number) => void,
  priority: number = 0,
  dependencies: React.DependencyList = []
) {
  const callbackRef = React.useRef(callback);
  const idRef = React.useRef(id);

  // Update callback ref when dependencies change
  React.useEffect(() => {
    callbackRef.current = callback;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [callback, ...dependencies]);

  React.useEffect(() => {
    // Only register animations in browser environment
    if (typeof window !== 'undefined') {
      const wrappedCallback = (time: number, deltaTime: number) => {
        callbackRef.current(time, deltaTime);
      };

      animationManager.register(idRef.current, wrappedCallback, priority);

      return () => {
        animationManager.unregister(idRef.current);
      };
    }
  }, [priority]);

  // Update ID if it changes
  React.useEffect(() => {
    if (idRef.current !== id) {
      animationManager.unregister(idRef.current);
      idRef.current = id;
      
      const wrappedCallback = (time: number, deltaTime: number) => {
        callbackRef.current(time, deltaTime);
      };
      
      animationManager.register(id, wrappedCallback, priority);
    }
  }, [id, priority]);
}

// Simple time-only hook for components that just need time
export function useAnimationTime(): number {
  const [time, setTime] = React.useState(0);
  
  useAnimation('time-only-' + React.useId(), (currentTime) => {
    setTime(currentTime);
  }, 999); // Low priority since it's just time updates

  return time;
}
