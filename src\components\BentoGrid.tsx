'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useAnimation } from '../utils/AnimationManager';

interface BentoGridProps {
  children: ReactNode;
  className?: string;
  adaptive?: boolean;
}

interface BentoItemProps {
  children: ReactNode;
  size: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  floating?: boolean;
  magnetic?: boolean;
  priority?: number;
}

export function BentoGrid({ children, className = '', adaptive = true }: BentoGridProps) {
  const [time, setTime] = useState(0);
  
  useAnimation('bento-grid', (currentTime) => {
    setTime(currentTime);
  }, 10);

  return (
    <div 
      className={`
        grid grid-cols-12 gap-4 md:gap-6 lg:gap-8 
        auto-rows-[minmax(200px,auto)]
        ${adaptive ? 'adaptive-grid' : ''}
        ${className}
      `}
      style={{
        transform: `translateY(${Math.sin(time * 0.1) * 2}px)`,
      }}
    >
      {children}
    </div>
  );
}

export function BentoItem({ 
  children, 
  size, 
  className = '', 
  floating = false, 
  magnetic = false,
  priority = 0 
}: BentoItemProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [time, setTime] = useState(0);

  useAnimation(`bento-item-${priority}`, (currentTime) => {
    setTime(currentTime);
  }, priority);

  const sizeClasses = {
    sm: 'col-span-6 md:col-span-3 row-span-1',
    md: 'col-span-6 md:col-span-4 row-span-2',
    lg: 'col-span-12 md:col-span-6 row-span-2',
    xl: 'col-span-12 md:col-span-8 row-span-3'
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!magnetic) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
    const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
    
    setMousePosition({ x: x * 20, y: y * 20 });
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  return (
    <div
      className={`
        ${sizeClasses[size]}
        relative group cursor-pointer
        transition-all duration-500 ease-out
        ${floating ? 'animate-float-slow' : ''}
        ${className}
      `}
      style={{
        transform: `
          translate3d(${mousePosition.x}px, ${mousePosition.y}px, 0)
          ${floating ? `translateY(${Math.sin(time * 0.3 + priority) * 5}px)` : ''}
          ${isHovered ? 'scale(1.02)' : 'scale(1)'}
        `,
        transformStyle: 'preserve-3d',
        animationDelay: `${priority * 0.1}s`,
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
    >
      {/* Magnetic Field Effect */}
      {magnetic && isHovered && (
        <div 
          className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none"
          style={{
            transform: `translate3d(${-mousePosition.x * 0.5}px, ${-mousePosition.y * 0.5}px, 0)`,
          }}
        />
      )}
      
      {/* Content Container */}
      <div className="relative h-full w-full">
        {children}
      </div>

      {/* Hover Glow Effect */}
      {isHovered && (
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-neon-pink/10 via-neon-cyan/10 to-neon-green/10 pointer-events-none transition-opacity duration-300" />
      )}
    </div>
  );
}

// Specialized Bento Components
export function BentoCard({ 
  children, 
  title, 
  subtitle, 
  icon, 
  className = '',
  glowing = false 
}: {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  className?: string;
  glowing?: boolean;
}) {
  return (
    <div className={`
      glass-ultra rounded-3xl p-6 md:p-8 h-full
      border border-white/20 relative overflow-hidden
      ${glowing ? 'animate-pulse-glow' : ''}
      ${className}
    `}>
      {/* Header */}
      {(title || subtitle || icon) && (
        <div className="flex items-start justify-between mb-6">
          <div className="space-y-2">
            {subtitle && (
              <div className="text-white/60 text-sm font-medium uppercase tracking-wide">
                {subtitle}
              </div>
            )}
            {title && (
              <h3 className="text-xl md:text-2xl font-black text-white leading-tight">
                {title}
              </h3>
            )}
          </div>
          {icon && (
            <div className="text-2xl md:text-3xl opacity-80">
              {icon}
            </div>
          )}
        </div>
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Shimmer Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
    </div>
  );
}

export function BentoMetric({ 
  value, 
  label, 
  trend, 
  color = 'neon-cyan',
  animated = true 
}: {
  value: string | number;
  label: string;
  trend?: 'up' | 'down' | 'stable';
  color?: string;
  animated?: boolean;
}) {
  const [displayValue, setDisplayValue] = useState(0);
  const numericValue = typeof value === 'string' ? parseInt(value.replace(/\D/g, '')) : value;

  useEffect(() => {
    if (!animated || typeof value === 'string') return;
    
    let current = 0;
    const increment = numericValue / 60;
    const timer = setInterval(() => {
      current += increment;
      if (current >= numericValue) {
        current = numericValue;
        clearInterval(timer);
      }
      setDisplayValue(Math.floor(current));
    }, 50);

    return () => clearInterval(timer);
  }, [numericValue, animated, value]);

  const trendIcons = {
    up: '↗',
    down: '↘',
    stable: '→'
  };

  const trendColors = {
    up: 'text-neon-green',
    down: 'text-neon-pink',
    stable: 'text-white/60'
  };

  return (
    <div className="space-y-3">
      <div className={`text-3xl md:text-4xl font-black text-${color} leading-none`}>
        {animated && typeof value === 'number' ? displayValue : value}
        {typeof value === 'string' && value.includes('+') && '+'}
      </div>
      <div className="flex items-center justify-between">
        <div className="text-white/80 text-sm font-medium">{label}</div>
        {trend && (
          <div className={`text-lg ${trendColors[trend]}`}>
            {trendIcons[trend]}
          </div>
        )}
      </div>
      <div className={`w-full h-1 bg-gradient-to-r from-${color} to-transparent rounded-full`} />
    </div>
  );
}

export function BentoImage({ 
  src, 
  alt, 
  overlay = true, 
  className = '' 
}: {
  src?: string;
  alt: string;
  overlay?: boolean;
  className?: string;
}) {
  return (
    <div className={`relative w-full h-full rounded-2xl overflow-hidden ${className}`}>
      {/* Placeholder for now */}
      <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white/40 text-center">
          <div className="text-4xl mb-2">📸</div>
          <div className="text-sm font-medium">{alt}</div>
        </div>
      </div>
      
      {overlay && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
      )}
    </div>
  );
}
