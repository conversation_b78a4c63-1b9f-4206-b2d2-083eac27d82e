import { useState, useEffect } from 'react';
import ImagePlaceholder from './ImagePlaceholder';
import HyperButton from './HyperButton';
import HyperBadge from './HyperBadge';
import { useAnimation } from '../utils/AnimationManager';

export default function MerchSection() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null);

  // Use centralized animation system
  const [time, setTime] = useState(0);
  useAnimation('merch-section', (currentTime) => {
    setTime(currentTime);
  }, 3); // Lower priority

  const products = [
    {
      name: 'Conscious Hoodie',
      price: '$89',
      description: 'Premium organic cotton with holographic CC logo',
      color: 'from-neon-pink to-neon-purple',
      icon: '👕'
    },
    {
      name: 'Neon Cap',
      price: '$45',
      description: 'Glow-in-the-dark embroidered design',
      color: 'from-neon-cyan to-neon-blue',
      icon: '🧢'
    },
    {
      name: 'Energy Tote',
      price: '$35',
      description: 'Sustainable canvas with LED accent strip',
      color: 'from-neon-green to-neon-yellow',
      icon: '👜'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('#merch-section');
    if (section) {
      observer.observe(section);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!email) return;

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitted(true);
      setIsLoading(false);
      setEmail('');
    }, 1000);
  };

  return (
    <section id="merch-section" className="section-padding-large bg-black relative overflow-hidden">
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Morphing Product Auras */}
        <div
          className="absolute w-96 h-96 rounded-full opacity-15 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 215, 0, 0.6) 0%, transparent 70%)',
            left: `${25 + Math.sin(time * 0.4) * 20}%`,
            top: `${20 + Math.cos(time * 0.3) * 15}%`,
            transform: `scale(${1 + Math.sin(time * 0.6) * 0.3}) rotate(${time * 12}deg)`,
          }}
        />
        <div
          className="absolute w-80 h-80 rounded-full opacity-12 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.6) 0%, transparent 70%)',
            right: `${20 + Math.cos(time * 0.5) * 18}%`,
            bottom: `${25 + Math.sin(time * 0.7) * 12}%`,
            transform: `scale(${1 + Math.cos(time * 0.4) * 0.2}) rotate(${-time * 8}deg)`,
          }}
        />

        {/* Shopping Grid Pattern */}
        <div className="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="shopping-grid" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
                <rect x="0" y="0" width="120" height="120" fill="none" stroke="rgba(255, 255, 255, 0.1)" strokeWidth="1" />
                <circle cx="60" cy="60" r="3" fill="rgba(255, 215, 0, 0.4)" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#shopping-grid)" />
          </svg>
        </div>
      </div>

      <div className="content-container relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Enhanced Section Header */}
          <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="mb-8">
              <HyperBadge
                type="new"
                text="MERCH COLLECTION"
                size="lg"
              />
            </div>

            <h2 className="mb-8 text-display-lg md:text-display-md font-black text-white leading-none tracking-tight">
              CONSCIOUS
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-gradient-x">
                GEAR
              </span>
            </h2>

            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-tight font-medium">
              Premium streetwear and accessories designed for the Conscious Collective. Coming soon to elevate your style.
            </p>
          </div>

          {/* 3D Product Showcase */}
          <div className={`grid md:grid-cols-3 gap-8 mb-16 transition-all duration-1000 delay-300 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            {products.map((product, index) => (
              <div
                key={index}
                className="relative group perspective-1000"
                onMouseEnter={() => setHoveredProduct(index)}
                onMouseLeave={() => setHoveredProduct(null)}
              >
                {/* Floating Badge - Only show on first product - Moved outside to prevent overflow clipping */}
                {index === 0 && (
                  <div className="absolute -top-3 -right-3 z-30">
                    <HyperBadge type="limited" size="sm" />
                  </div>
                )}

                {/* 3D Product Card */}
                <div
                  className="relative transform-3d transition-all duration-700 cursor-pointer"
                  style={{
                    transform: hoveredProduct === index
                      ? 'translateZ(30px) rotateX(10deg) rotateY(5deg) scale(1.05)'
                      : 'translateZ(0px) rotateX(0deg) rotateY(0deg) scale(1)',
                  }}
                >
                  {/* Product Image Container */}
                  <div className="relative glass rounded-3xl p-8 border border-white/10 group-hover:border-white/30 transition-all duration-500 overflow-hidden">
                    {/* Energy Field Background */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${product.color} opacity-10 group-hover:opacity-20 transition-opacity duration-500`}
                    />

                    {/* Product Mockup */}
                    <div className="relative z-10">
                      <ImagePlaceholder
                        aspectRatio="square"
                        className="w-full rounded-2xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                        alt={`${product.name} mockup`}
                      >
                        <div className="text-center">
                          <div className={`w-20 h-20 bg-gradient-to-r ${product.color} rounded-full flex items-center justify-center mx-auto mb-4 text-3xl group-hover:scale-110 transition-transform duration-300`}>
                            {product.icon}
                          </div>
                          <p className="text-sm text-white/60">{product.name}</p>
                        </div>
                      </ImagePlaceholder>
                    </div>

                    {/* Particle Burst Effect */}
                    {hoveredProduct === index && (
                      <div className="absolute inset-0 pointer-events-none">
                        {[...Array(12)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-1 h-1 rounded-full animate-ping"
                            style={{
                              backgroundColor: product.color.split(' ')[1],
                              left: `${20 + Math.random() * 60}%`,
                              top: `${20 + Math.random() * 60}%`,
                              animationDelay: `${Math.random() * 0.5}s`,
                            }}
                          />
                        ))}
                      </div>
                    )}

                    {/* Holographic Scanning Line */}
                    {hoveredProduct === index && (
                      <div
                        className="absolute top-0 left-0 w-full h-0.5 bg-white opacity-80"
                        style={{
                          animation: 'scan 2s ease-in-out infinite',
                        }}
                      />
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="mt-6 text-center">
                    <h3 className="text-xl font-black text-white uppercase tracking-wide mb-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-yellow-400 group-hover:to-orange-500 transition-all duration-500">
                      {product.name}
                    </h3>
                    <p className="text-white/60 text-sm mb-3 group-hover:text-white/80 transition-colors duration-300">
                      {product.description}
                    </p>
                    <div className="text-2xl font-black text-yellow-400 group-hover:scale-110 transition-transform duration-300">
                      {product.price}
                    </div>
                  </div>

                  {/* Magnetic Glow Effect */}
                  <div
                    className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
                    style={{
                      boxShadow: hoveredProduct === index
                        ? `0 0 40px ${product.color.split(' ')[1]}, 0 0 80px ${product.color.split(' ')[2] || product.color.split(' ')[1]}`
                        : 'none',
                    }}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Ultra-Modern Email Capture */}
          <div className={`max-w-2xl mx-auto transition-all duration-1000 delay-500 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="glass rounded-3xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500 relative overflow-hidden">
              {/* Background Energy Field */}
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 via-orange-500/10 to-red-500/10 opacity-50" />

              <div className="relative z-10 space-y-8">
                <div className="text-center">
                  <h3 className="text-2xl font-black text-white uppercase tracking-wide mb-4">
                    Get Notified First
                  </h3>
                  <p className="text-white/70 text-lg leading-relaxed">
                    Be the first to know when our exclusive merch collection drops.
                    Premium streetwear and accessories for the Conscious Collective.
                  </p>
                </div>

                {/* Enhanced Email Form */}
                {!isSubmitted ? (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="relative">
                      <label htmlFor="email" className="sr-only">
                        Email address
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter your email for early access"
                        className="w-full px-6 py-4 bg-black/50 border border-white/20 rounded-2xl text-white placeholder-white/50 focus:border-yellow-400 focus:outline-none focus:ring-2 focus:ring-yellow-400/20 transition-all duration-300"
                        required
                      />
                      {/* Input Glow Effect */}
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-yellow-400/20 to-orange-500/20 opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
                    </div>

                    <HyperButton
                      variant="quantum"
                      size="lg"
                      onClick={handleSubmit}
                      disabled={isLoading}
                      loading={isLoading}
                      icon="🔔"
                      className="w-full"
                    >
                      {isLoading ? 'Subscribing...' : 'Notify Me'}
                    </HyperButton>
                  </form>
                ) : (
                  <div className="text-center p-8 glass rounded-2xl border border-green-400/30 bg-green-400/10">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-glow">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h4 className="font-black text-green-400 text-xl mb-2 uppercase tracking-wide">
                      You&apos;re on the list!
                    </h4>
                    <p className="text-green-300 text-lg">
                      We&apos;ll notify you as soon as our merch drops.
                    </p>
                  </div>
                )}

                {/* Shop Link */}
                <div className="text-center">
                  <p className="text-white/60 text-sm">
                    Launching at{' '}
                    <span className="font-black text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500 animate-gradient-x">
                      shop.consciouscollective.com
                    </span>
                  </p>
                </div>
              </div>

              {/* Floating Particles */}
              <div className="absolute inset-0 pointer-events-none">
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-float-slow opacity-40"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 4}s`,
                      animationDuration: `${4 + Math.random() * 2}s`,
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
