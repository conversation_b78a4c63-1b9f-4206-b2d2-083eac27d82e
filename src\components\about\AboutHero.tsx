'use client';

import { useState, useEffect, useRef } from 'react';
import HyperBadge from '../HyperBadge';
import HyperButton from '../HyperButton';

export default function AboutHero() {
  const [isVisible, setIsVisible] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const heroRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '100px' // Start animations earlier for smoother experience
      }
    );

    if (heroRef.current) {
      observer.observe(heroRef.current);
    }

    // Preload critical images for better performance
    const preloadImages = () => {
      const imageUrls = [
        // Add any critical images that should be preloaded
      ];

      imageUrls.forEach(url => {
        const img = new Image();
        img.src = url;
      });
    };

    // Use requestIdleCallback for non-critical preloading
    if ('requestIdleCallback' in window) {
      requestIdleCallback(preloadImages);
    } else {
      setTimeout(preloadImages, 100);
    }

    // Optimized scroll listener for parallax effects
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setScrollY(window.scrollY);
          ticking = false;
        });
        ticking = true;
      }
    };

    // Add passive scroll listener for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const scrollToStory = () => {
    const storySection = document.querySelector('#our-story');
    if (storySection) {
      storySection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-black"
      style={{
        transform: `translateY(${scrollY * 0.05}px)`, // Subtle parallax effect
      }}
    >
      {/* Revolutionary 2025 Background */}
      <div className="absolute inset-0">
        {/* Animated Gradient Base */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
          <div className="absolute inset-0 bg-gradient-to-r from-neon-pink/10 via-neon-cyan/10 to-neon-green/10 animate-gradient-x" />
        </div>
        
        {/* Floating Energy Orbs */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-neon-pink/20 to-neon-purple/20 rounded-full blur-3xl animate-float-slow" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-neon-cyan/15 to-neon-blue/15 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-neon-green/10 to-neon-yellow/10 rounded-full blur-3xl animate-pulse" />
      </div>

      {/* Clean Centered Layout */}
      <div className="relative z-20 w-full max-w-7xl mx-auto px-4 md:px-6">
        <div className="flex flex-col items-center justify-center min-h-screen text-center space-y-8 md:space-y-12 pb-20 md:pb-32">

          {/* Header Badge */}
          <div className={`flex justify-center w-full transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <HyperBadge
              type="featured"
              text="ABOUT COLLECTIV"
              size="lg"
            />
          </div>

          <div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-none tracking-tight mb-6">
              <span className="block text-white mb-2 md:mb-4">MANIFESTING</span>
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-purple to-neon-cyan animate-gradient-x mb-2 md:mb-4">
                EUPHORIA
              </span>
              <span className="block text-white text-lg sm:text-xl md:text-2xl lg:text-3xl font-medium opacity-80">SINCE 2024</span>
            </h1>
          </div>

          {/* Subtitle */}
          <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <p className="text-base sm:text-lg md:text-xl font-semibold text-white/70 leading-relaxed max-w-4xl mx-auto mb-8">
              We&apos;re not just throwing parties — we&apos;re cultivating a movement of
              <span className="text-neon-cyan"> conscious experiences</span> that transform souls and
              <span className="text-neon-pink"> unite communities</span> across San Diego.
            </p>
          </div>

          {/* Key Pillars */}
          <div className={`transition-all duration-1000 delay-600 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="flex flex-wrap gap-3 justify-center max-w-4xl mx-auto mb-12">
              {[
                { text: 'Conscious Celebration', icon: '✨' },
                { text: 'Community First', icon: '🤝' },
                { text: 'Transformative Experiences', icon: '🌟' },
                { text: 'Mindful Movement', icon: '🧘' }
              ].map((pillar, i) => (
                <div
                  key={i}
                  className="group px-4 py-2 glass rounded-full border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{pillar.icon}</span>
                    <span className="text-sm font-bold text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-neon-pink group-hover:to-neon-cyan transition-all duration-300">
                      {pillar.text}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Buttons */}
          <div className={`transition-all duration-1000 delay-800 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <HyperButton
                variant="plasma"
                size="lg"
                onClick={scrollToStory}
                icon="📖"
                soundEnabled={true}
                className="w-full sm:w-auto"
              >
                Our Story
              </HyperButton>

              <HyperButton
                variant="holographic"
                size="lg"
                onClick={() => {
                  const partnerSection = document.querySelector('#partner-with-us');
                  if (partnerSection) {
                    partnerSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                icon="🤝"
                soundEnabled={true}
                className="w-full sm:w-auto"
              >
                Partner With Us
              </HyperButton>
            </div>
          </div>

          {/* Manifesto Quote */}
          <div className={`transition-all duration-1000 delay-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="glass-ultra rounded-3xl p-6 md:p-8 border border-white/20 max-w-2xl mx-auto">
              <div className="space-y-4">
                <div className="text-3xl md:text-4xl text-neon-yellow">&ldquo;</div>
                <p className="text-white/80 text-base md:text-lg font-medium leading-relaxed">
                  Dance floors are sacred spaces where souls connect and consciousness expands.
                </p>
                <div className="text-white/60 text-sm font-semibold">— Conscious Collectiv manifesto</div>
              </div>
            </div>
          </div>

        </div>


      </div>
    </section>
  );
}
