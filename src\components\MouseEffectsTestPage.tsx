'use client';

import { useState } from 'react';
import HyperButton from './HyperButton';
import HyperBadge from './HyperBadge';

export default function MouseEffectsTestPage() {
  const [testResults, setTestResults] = useState<{
    fps: number;
    memoryUsage: number;
    animationCount: number;
    mouseEvents: number;
  }>({
    fps: 0,
    memoryUsage: 0,
    animationCount: 0,
    mouseEvents: 0
  });

  const [isStressTest, setIsStressTest] = useState(false);

  const runStressTest = () => {
    setIsStressTest(true);
    
    // Create multiple animated elements to stress test
    const container = document.createElement('div');
    container.className = 'stress-test-container';
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = '100vw';
    container.style.height = '100vh';
    container.style.pointerEvents = 'none';
    container.style.zIndex = '9990';

    // Add 50 animated elements
    for (let i = 0; i < 50; i++) {
      const element = document.createElement('div');
      element.style.position = 'absolute';
      element.style.width = '20px';
      element.style.height = '20px';
      element.style.background = `hsl(${i * 7}, 70%, 50%)`;
      element.style.borderRadius = '50%';
      element.style.left = `${Math.random() * 100}%`;
      element.style.top = `${Math.random() * 100}%`;
      element.style.animation = `float ${2 + Math.random() * 3}s ease-in-out infinite`;
      element.className = 'stress-test-element';
      container.appendChild(element);
    }

    document.body.appendChild(container);

    // Remove after 10 seconds
    setTimeout(() => {
      document.body.removeChild(container);
      setIsStressTest(false);
    }, 10000);
  };

  return (
    <div className="min-h-screen bg-black text-white p-8">
      {/* Test Header */}
      <div className="max-w-6xl mx-auto">
        <h1 className="text-6xl font-black text-3d-neon mb-8 text-center">
          MOUSE EFFECTS TEST LAB
        </h1>
        
        <div className="text-center mb-12">
          <p className="text-xl text-white/80 mb-6">
            Test and validate the optimized mouse animations and effects
          </p>
          
          {/* Performance Metrics Display */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="glass-ultra p-4 rounded-xl">
              <div className="text-2xl font-bold text-neon-cyan">{testResults.fps}</div>
              <div className="text-sm text-white/60">FPS</div>
            </div>
            <div className="glass-ultra p-4 rounded-xl">
              <div className="text-2xl font-bold text-neon-green">{testResults.memoryUsage}</div>
              <div className="text-sm text-white/60">Memory (MB)</div>
            </div>
            <div className="glass-ultra p-4 rounded-xl">
              <div className="text-2xl font-bold text-neon-yellow">{testResults.animationCount}</div>
              <div className="text-sm text-white/60">Animations</div>
            </div>
            <div className="glass-ultra p-4 rounded-xl">
              <div className="text-2xl font-bold text-neon-pink">{testResults.mouseEvents}</div>
              <div className="text-sm text-white/60">Mouse Events/s</div>
            </div>
          </div>
        </div>

        {/* Interactive Test Elements */}
        <div className="space-y-12">
          
          {/* Button Test Section */}
          <section className="glass-ultra p-8 rounded-3xl">
            <h2 className="text-3xl font-black mb-6 text-neon-cyan">HYPERBUTTON TESTS</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <HyperButton variant="primary" size="md">
                Primary
              </HyperButton>
              <HyperButton variant="magnetic" size="md">
                Magnetic
              </HyperButton>
              <HyperButton variant="quantum" size="md">
                Quantum
              </HyperButton>
              <HyperButton variant="holographic" size="md">
                Holographic
              </HyperButton>
              <HyperButton variant="plasma" size="md">
                Plasma
              </HyperButton>
              <HyperButton variant="neon" size="md">
                Neon
              </HyperButton>
              <HyperButton variant="ghost" size="md">
                Ghost
              </HyperButton>
              <HyperButton variant="secondary" size="md">
                Secondary
              </HyperButton>
            </div>
          </section>

          {/* Badge Test Section */}
          <section className="glass-ultra p-8 rounded-3xl">
            <h2 className="text-3xl font-black mb-6 text-neon-green">HYPERBADGE TESTS</h2>
            <div className="flex flex-wrap gap-4">
              <HyperBadge type="live" isAnimated={true} />
              <HyperBadge type="sold-out" isAnimated={true} />
              <HyperBadge type="early-bird" isAnimated={true} />
              <HyperBadge type="vip" isAnimated={true} />
              <HyperBadge type="featured" isAnimated={true} />
              <HyperBadge type="new" isAnimated={true} />
              <HyperBadge type="trending" isAnimated={true} />
              <HyperBadge type="limited" count={5} maxCount={10} isAnimated={true} />
            </div>
          </section>

          {/* Cursor Test Section */}
          <section className="glass-ultra p-8 rounded-3xl">
            <h2 className="text-3xl font-black mb-6 text-neon-yellow">CURSOR INTERACTION TESTS</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              
              {/* Hover Test Area */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-white/80">Hover Effects</h3>
                <div className="cursor-glow p-6 border border-white/20 rounded-xl hover:border-neon-cyan transition-all duration-300">
                  <p>Hover over this area to test glow cursor</p>
                </div>
                <div className="glass p-6 rounded-xl">
                  <p>Glass element with special cursor</p>
                </div>
              </div>

              {/* Link Test Area */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-white/80">Link Effects</h3>
                <div className="space-y-2">
                  <a href="#" className="block text-neon-cyan hover:text-neon-pink transition-colors">
                    Test Link 1
                  </a>
                  <a href="#" className="block text-neon-green hover:text-neon-yellow transition-colors">
                    Test Link 2
                  </a>
                  <a href="#" className="block text-neon-purple hover:text-neon-orange transition-colors">
                    Test Link 3
                  </a>
                </div>
              </div>

              {/* Input Test Area */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-white/80">Input Effects</h3>
                <input 
                  type="text" 
                  placeholder="Test input field"
                  className="w-full p-3 bg-black/50 border border-white/20 rounded-lg text-white placeholder-white/40 focus:border-neon-cyan focus:outline-none"
                />
                <textarea 
                  placeholder="Test textarea"
                  className="w-full p-3 bg-black/50 border border-white/20 rounded-lg text-white placeholder-white/40 focus:border-neon-cyan focus:outline-none h-20 resize-none"
                />
              </div>
            </div>
          </section>

          {/* Performance Test Section */}
          <section className="glass-ultra p-8 rounded-3xl">
            <h2 className="text-3xl font-black mb-6 text-neon-pink">PERFORMANCE STRESS TESTS</h2>
            <div className="space-y-6">
              <div className="flex flex-col md:flex-row gap-4 items-center">
                <HyperButton 
                  variant="plasma" 
                  size="lg"
                  onClick={runStressTest}
                  disabled={isStressTest}
                >
                  {isStressTest ? 'RUNNING STRESS TEST...' : 'START STRESS TEST'}
                </HyperButton>
                <p className="text-white/60">
                  Creates 50 animated elements for 10 seconds to test performance
                </p>
              </div>
              
              {isStressTest && (
                <div className="bg-red-500/20 border border-red-500/40 rounded-lg p-4">
                  <p className="text-red-300 font-bold">⚠️ Stress test running - Monitor performance metrics above</p>
                </div>
              )}
            </div>
          </section>

          {/* Controls Guide */}
          <section className="glass-ultra p-8 rounded-3xl">
            <h2 className="text-3xl font-black mb-6 text-neon-purple">KEYBOARD CONTROLS</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-bold mb-4 text-neon-cyan">Cursor Effects</h3>
                <ul className="space-y-2 text-white/80">
                  <li><kbd className="bg-white/20 px-2 py-1 rounded">Ctrl+Shift+E</kbd> - Toggle Advanced Effects</li>
                  <li><kbd className="bg-white/20 px-2 py-1 rounded">Tab</kbd> - Cycle Effect Modes</li>
                  <li><kbd className="bg-white/20 px-2 py-1 rounded">Ctrl+Shift+1/2/3</kbd> - Performance Quality</li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-4 text-neon-green">Performance</h3>
                <ul className="space-y-2 text-white/80">
                  <li><kbd className="bg-white/20 px-2 py-1 rounded">Ctrl+Shift+P</kbd> - Toggle Performance Monitor</li>
                  <li><kbd className="bg-white/20 px-2 py-1 rounded">F12</kbd> - Open DevTools for detailed metrics</li>
                </ul>
              </div>
            </div>
          </section>

        </div>
      </div>

      {/* Floating Animation Keyframes */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-10px) rotate(90deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
          75% { transform: translateY(-10px) rotate(270deg); }
        }
      `}</style>
    </div>
  );
}
