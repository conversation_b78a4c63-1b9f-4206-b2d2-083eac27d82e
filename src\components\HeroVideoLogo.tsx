'use client';

import { useState, useEffect, useRef, memo, useCallback } from 'react';

interface HeroVideoLogoProps {
  className?: string;
}

const HeroVideoLogo = memo(function HeroVideoLogo({ className = '' }: HeroVideoLogoProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [videoSrc, setVideoSrc] = useState('');
  const [placeholderSrc, setPlaceholderSrc] = useState('');
  const [showPlaceholder, setShowPlaceholder] = useState(true);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const placeholderRef = useRef<HTMLImageElement>(null);
  const hasInitialized = useRef(false);

  // Simplified video selection - no dynamic switching to prevent flickering
  const initializeVideo = useCallback(() => {
    if (hasInitialized.current) return;

    hasInitialized.current = true;

    // Simple device detection
    const isMobile = window.innerWidth < 768;

    // Set video and placeholder once
    const selectedVideoSrc = isMobile
      ? '/images/optimized/hero_logo_4s.mp4' // ~1MB - 4-second loop
      : '/images/optimized/hero_logo_high_4s.mp4'; // ~2.5MB - 4-second loop

    const selectedPlaceholderSrc = isMobile
      ? '/images/optimized/hero_logo_frame1.webp' // ~30KB - instant
      : '/images/optimized/hero_logo_high_frame1.webp'; // ~50KB - instant

    setVideoSrc(selectedVideoSrc);
    setPlaceholderSrc(selectedPlaceholderSrc);
  }, []);

  useEffect(() => {
    if (hasInitialized.current) return;
    hasInitialized.current = true;

    // Simple device detection
    const isMobile = window.innerWidth < 768;

    // Set both placeholder and video sources immediately
    const selectedPlaceholderSrc = isMobile
      ? '/images/optimized/hero_logo_frame1.webp'
      : '/images/optimized/hero_logo_high_frame1.webp';

    const selectedVideoSrc = isMobile
      ? '/images/optimized/hero_logo_4s.mp4'
      : '/images/optimized/hero_logo_high_4s.mp4';

    console.log('HeroVideoLogo: Setting sources - placeholder:', selectedPlaceholderSrc, 'video:', selectedVideoSrc);

    setPlaceholderSrc(selectedPlaceholderSrc);
    setVideoSrc(selectedVideoSrc);
  }, []);

  // Simplified visibility - always visible since it's the hero
  useEffect(() => {
    // Set visible after a brief delay to ensure smooth initial render
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleVideoLoad = useCallback(() => {
    console.log('HeroVideoLogo: Video loaded successfully');
    if (!isLoaded && videoRef.current) {
      setIsLoaded(true);
      setIsVideoReady(true);

      // Play the video
      videoRef.current.play().catch((error) => {
        console.log('Autoplay prevented:', error);
      });

      // Hide placeholder after a short delay to allow smooth transition
      setTimeout(() => {
        console.log('HeroVideoLogo: Swapping from placeholder to video');
        setShowPlaceholder(false);
      }, 300);
    }
  }, [isLoaded]);

  const handleVideoError = useCallback(() => {
    console.warn('Video failed to load, keeping placeholder visible');
    // Don't retry to prevent infinite loops, just keep placeholder
    setIsVideoReady(false);
    setShowPlaceholder(true);
  }, []);

  return (
    <div className={`relative ${className}`} style={{ backgroundColor: 'transparent' }}>
      {/* Background Gradient Layers */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Primary gradient backdrop */}
        <div
          className="absolute inset-0 opacity-60"
          style={{
            background: `
              radial-gradient(circle at 30% 40%, rgba(255, 0, 128, 0.4) 0%, transparent 50%),
              radial-gradient(circle at 70% 60%, rgba(0, 255, 255, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 50% 80%, rgba(255, 255, 0, 0.2) 0%, transparent 50%)
            `,
          }}
        />
        
        {/* Animated mesh overlay */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              conic-gradient(from 0deg at 50% 50%,
                rgba(255, 0, 128, 0.3) 0deg,
                rgba(0, 255, 255, 0.2) 120deg,
                rgba(255, 255, 0, 0.2) 240deg,
                rgba(255, 0, 128, 0.3) 360deg)
            `,
            filter: 'blur(40px)',
          }}
        />
      </div>

      {/* Video Container with Advanced Blend Modes */}
      <div className={`relative z-10 transition-all duration-1000 ${isVisible ? 'animate-scale-in-bounce' : 'opacity-0 scale-50'}`}>
        {/* Holographic frame */}
        <div className="absolute inset-0 rounded-3xl border-2 border-transparent bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-yellow p-0.5">
          <div className="w-full h-full bg-black rounded-3xl" />
        </div>

        {/* PHASE 1: Frame-1 Placeholder - Loads instantly */}
        {placeholderSrc && (
          <img
            ref={placeholderRef}
            src={placeholderSrc}
            alt="Hero logo"
            className={`
              absolute inset-0 z-20 w-full h-auto max-w-2xl mx-auto rounded-3xl
              transition-opacity duration-500 ease-in-out
              ${showPlaceholder ? 'opacity-100' : 'opacity-0'}
            `}
            style={{
              mixBlendMode: 'screen',
              filter: 'contrast(1.2) brightness(1.1) saturate(1.1)',
            }}
          />
        )}

        {/* Main video element */}
        {videoSrc && (
          <video
            ref={videoRef}
            src={videoSrc} // videoSrc is only set after delay
            autoPlay
            loop
            muted
            playsInline
            preload="metadata" // Load metadata to enable faster playback
            onLoadedData={handleVideoLoad}
            onCanPlay={handleVideoLoad}
            onError={handleVideoError}
            className={`
              relative z-20 w-full h-auto max-w-2xl mx-auto rounded-3xl
              transition-opacity duration-500 ease-in-out
              ${isVideoReady && !showPlaceholder ? 'opacity-100' : 'opacity-0'}
            `}
            style={{
              mixBlendMode: 'screen', // This removes black background!
              filter: isLoaded
                ? 'contrast(1.1) brightness(1.05) saturate(1.05)'
                : 'contrast(1) brightness(1) saturate(1)',
              backgroundColor: 'transparent', // Ensure no black background
            }}
          />
        )}

        {/* Purple mask overlay for additional styling */}
        <div
          className="absolute inset-0 z-30 rounded-3xl pointer-events-none opacity-60"
          style={{
            background: `
              radial-gradient(circle at 50% 50%,
                rgba(138, 43, 226, 0.1) 0%,
                rgba(255, 0, 128, 0.05) 40%,
                transparent 70%)
            `,
            mixBlendMode: 'multiply',
          }}
        />

        {/* Holographic shimmer effect */}
        <div
          className="absolute inset-0 z-40 rounded-3xl pointer-events-none opacity-30"
          style={{
            background: `
              linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 70%
              )
            `,
          }}
        />




      </div>



      {/* Fallback text for accessibility */}
      <div className="sr-only">collectiv logo animation</div>
    </div>
  );
});

export default HeroVideoLogo;
