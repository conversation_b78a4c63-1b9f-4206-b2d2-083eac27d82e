'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

// Throttle function for performance optimization
const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function(this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

export default function LogoCarousel() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const isVisibleRef = useRef(false);

  const venues = [
    { name: 'Waterfront Park', width: 'w-32', color: 'from-neon-pink to-neon-purple', particles: 12 },
    { name: 'The Soap Factory', width: 'w-28', color: 'from-neon-cyan to-neon-blue', particles: 10 },
    { name: 'Quartyard', width: 'w-24', color: 'from-neon-green to-neon-cyan', particles: 8 },
    { name: 'Beach House', width: 'w-26', color: 'from-neon-yellow to-neon-orange', particles: 9 },
    { name: 'Spin Nightclub', width: 'w-24', color: 'from-neon-purple to-neon-pink', particles: 8 },
    { name: 'EQ San Diego', width: 'w-28', color: 'from-neon-orange to-neon-red', particles: 10 },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        const visible = entry.isIntersecting;
        setIsVisible(visible);
        isVisibleRef.current = visible;
      },
      { threshold: 0.3 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Heavily optimized mouse move handler with aggressive throttling for performance
  const handleMouseMove = throttle((e: React.MouseEvent) => {
    if (!isVisibleRef.current || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const newX = e.clientX - rect.left;
    const newY = e.clientY - rect.top;

    // Only update if movement is significant (reduces unnecessary repaints)
    const threshold = 10; // pixels
    if (Math.abs(newX - mousePosition.x) > threshold || Math.abs(newY - mousePosition.y) > threshold) {
      setMousePosition({ x: newX, y: newY });
    }
  }, 100); // Reduced to 10fps for better performance

  // Optimized hover handlers
  const handleMouseEnter = (index: number) => {
    if (!isVisibleRef.current) return;
    setHoveredIndex(index);
  };

  const handleMouseLeave = () => {
    setHoveredIndex(null);
  };

  return (
    <section
      ref={containerRef}
      className="section-padding bg-black relative overflow-hidden transform-3d"
      onMouseMove={handleMouseMove}
    >
      {/* Ultra-Dynamic Background Effects */}
      <div className="absolute inset-0">
        {/* Animated Gradient Mesh */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-neon-pink/30 via-transparent to-neon-cyan/30 animate-gradient-x" />
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-tr from-neon-purple/20 via-transparent to-neon-green/20 animate-gradient-y" />
        </div>

        {/* Magnetic Field Visualization */}
        <div className="absolute inset-0 opacity-15">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-96 h-96 rounded-full animate-pulse-glow"
              style={{
                background: `radial-gradient(circle, ${venues[i]?.color.split(' ')[1]} 0%, transparent 70%)`,
                left: `${15 + i * 15}%`,
                top: `${20 + (i % 2) * 40}%`,
                transform: `translate(-50%, -50%) scale(${hoveredIndex === i ? 1.5 : 1})`,
                transition: 'transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1)',
                filter: 'blur(40px)',
              }}
            />
          ))}
        </div>

        {/* Particle System */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full animate-float-slow opacity-40"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 8}s`,
                animationDuration: `${6 + Math.random() * 6}s`,
                transform: hoveredIndex !== null ? `scale(${1 + Math.random()})` : 'scale(1)',
              }}
            />
          ))}
        </div>
      </div>

      <div className="content-container relative z-10">
        <div className="text-center mb-20">
          <div className={`transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <p className="text-neon-cyan font-black text-lg uppercase tracking-wider mb-6 animate-pulse-glow">
              Our Venues
            </p>
            <h3 className="text-white font-black text-display-sm leading-none tracking-tight">
              ICONIC SPACES ACROSS
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-purple to-neon-cyan animate-gradient-x">
                SAN DIEGO
              </span>
            </h3>
          </div>
        </div>

        {/* 3D Holographic Venue Cards */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center justify-items-center perspective-1000">
          {venues.map((venue, index) => (
            <div
              key={index}
              className="relative group cursor-pointer transform-3d"
              onMouseEnter={() => handleMouseEnter(index)}
              onMouseLeave={handleMouseLeave}
              style={{
                transform: hoveredIndex === index
                  ? 'translateZ(50px) rotateX(10deg) rotateY(5deg) scale(1.1)'
                  : 'translateZ(0px) rotateX(0deg) rotateY(0deg) scale(1)',
                transition: 'transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)',
              }}
            >
              {/* Holographic Base */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-white/1 backdrop-blur-xl border border-white/10 shadow-2xl" />

              {/* Energy Field */}
              <div
                className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${venue.color} opacity-20 blur-sm transition-all duration-500`}
                style={{
                  opacity: hoveredIndex === index ? 0.4 : 0.1,
                  transform: hoveredIndex === index ? 'scale(1.1)' : 'scale(1)',
                }}
              />

              {/* Particle Burst on Hover */}
              {hoveredIndex === index && (
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(venue.particles)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                      style={{
                        left: `${20 + Math.random() * 60}%`,
                        top: `${20 + Math.random() * 60}%`,
                        animationDelay: `${Math.random() * 0.5}s`,
                        animationDuration: '1s',
                      }}
                    />
                  ))}
                </div>
              )}

              {/* Main Card Content */}
              <div className={`${venue.width} h-16 relative z-10 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20 overflow-hidden group-hover:border-white/40 transition-all duration-500 px-4 py-3`}>
                {/* Scanning Line Effect */}
                <div
                  className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-pulse"
                  style={{
                    animation: hoveredIndex === index ? 'scan 2s ease-in-out infinite' : 'none',
                  }}
                />

                {/* Venue Name with Glitch Effect */}
                <span className={`text-xs font-black text-white uppercase tracking-wider transition-all duration-300 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r ${venue.color}`}>
                  {venue.name}
                </span>

                {/* Holographic Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              </div>

              {/* Magnetic Attraction Effect */}
              <div
                className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-white/30 transition-all duration-500"
                style={{
                  boxShadow: hoveredIndex === index
                    ? `0 0 30px ${venue.color.split(' ')[1]}, 0 0 60px ${venue.color.split(' ')[2] || venue.color.split(' ')[1]}`
                    : 'none',
                }}
              />
            </div>
          ))}
        </div>

        {/* Hyper-Modern Infinite Marquee */}
        <div className="hidden lg:block mt-24 relative">
          {/* Marquee Background Glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-neon-cyan/5 to-transparent blur-xl" />

          <div className="overflow-hidden relative">
            {/* Energy Stream */}
            <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-pink animate-gradient-x opacity-60" />

            <div className="flex animate-hyper-scroll space-x-16 py-6">
              {/* Triple set for ultra-smooth loop */}
              {[...Array(3)].map((_, setIndex) =>
                venues.map((venue, index) => (
                  <div
                    key={`set-${setIndex}-${index}`}
                    className="flex items-center justify-center flex-shrink-0 group cursor-pointer"
                    style={{
                      transform: `translateY(${Math.sin((setIndex * venues.length + index) * 0.5) * 10}px)`,
                      animation: `float ${4 + Math.random() * 2}s ease-in-out infinite`,
                      animationDelay: `${(setIndex * venues.length + index) * 0.2}s`,
                    }}
                  >
                    <div className={`${venue.width} h-10 relative rounded-xl flex items-center justify-center backdrop-blur-md border border-white/10 group-hover:border-white/30 transition-all duration-500 overflow-hidden px-3 py-2`}>
                      {/* Morphing Background */}
                      <div className={`absolute inset-0 bg-gradient-to-r ${venue.color} opacity-10 group-hover:opacity-30 transition-all duration-500`} />

                      {/* Data Stream Effect */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        {[...Array(5)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-px h-full bg-white/20 animate-pulse"
                            style={{
                              left: `${20 + i * 15}%`,
                              animationDelay: `${i * 0.1}s`,
                            }}
                          />
                        ))}
                      </div>

                      <span className="relative z-10 text-xs font-bold text-white/60 group-hover:text-white uppercase tracking-wider transition-all duration-500">
                        {venue.name}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Magnetic Mouse Follower */}
        {hoveredIndex !== null && (
          <div
            className="fixed pointer-events-none z-50 w-32 h-32 rounded-full opacity-30 blur-2xl transition-all duration-300"
            style={{
              background: `radial-gradient(circle, ${venues[hoveredIndex].color.split(' ')[1]} 0%, transparent 70%)`,
              left: mousePosition.x - 64,
              top: mousePosition.y - 64,
              transform: 'translate(-50%, -50%)',
            }}
          />
        )}
      </div>

      <style jsx>{`
        .perspective-1000 {
          perspective: 1000px;
        }

        @keyframes hyper-scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-33.333%);
          }
        }

        @keyframes scan {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }

        .animate-hyper-scroll {
          animation: hyper-scroll 45s linear infinite;
        }

        .transform-3d {
          transform-style: preserve-3d;
        }
      `}</style>
    </section>
  );
}
