'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useAnimation } from '../utils/AnimationManager';
import { throttle } from '../utils/throttle';

interface LogoProps {
  variant?: 'nav' | 'footer' | 'hero';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  className?: string;
  href?: string;
}

export default function Logo({
  variant = 'nav',
  size = 'md',
  showText = true,
  className = '',
  href = '/',
}: LogoProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const logoRef = useRef<HTMLDivElement>(null);

  // Use centralized animation system - only when hovered
  const [time, setTime] = useState(0);
  useAnimation('logo', (currentTime) => {
    if (isHovered) {
      setTime(currentTime);
    }
  }, 4, [isHovered]); // Lowest priority, depend on hover state

  // Reset animation time when not hovered
  useEffect(() => {
    if (!isHovered) {
      setTime(0);
    }
  }, [isHovered]);

  // Optimized mouse tracking for parallax effects with throttling
  const handleMouseMove = throttle((e: React.MouseEvent) => {
    if (!logoRef.current || !isHovered) return; // Only track when hovered

    const rect = logoRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width - 0.5;
    const y = (e.clientY - rect.top) / rect.height - 0.5;

    // Only update if movement is significant
    const threshold = 0.05;
    if (Math.abs(x - mousePosition.x) > threshold || Math.abs(y - mousePosition.y) > threshold) {
      setMousePosition({ x, y });
    }
  }, 100); // 10fps throttling

  // Size configurations - Increased sizes for better visibility
  const sizeConfig = {
    sm: {
      logo: 'w-10 h-10',
      text: 'text-sm',
      container: 'space-x-2',
    },
    md: {
      logo: variant === 'nav' ? 'w-14 h-14' : 'w-14 h-14', // Increased from w-10 h-10
      text: 'text-xl',
      container: 'space-x-3',
    },
    lg: {
      logo: 'w-20 h-20', // Increased from w-16 h-16
      text: 'text-2xl',
      container: 'space-x-4',
    },
    xl: {
      logo: 'w-28 h-28', // Increased from w-24 h-24
      text: 'text-4xl',
      container: 'space-x-6',
    },
  };

  // Variant-specific styling - Enhanced for cursive logo
  const variantConfig = {
    nav: {
      textColor: 'text-white',
      glowColor: 'rgba(255, 255, 255, 0.4)',
      hoverScale: 'hover:scale-110',
      shadowColor: 'rgba(255, 0, 128, 0.3)',
    },
    footer: {
      textColor: 'text-white',
      glowColor: 'rgba(0, 255, 255, 0.7)',
      hoverScale: 'hover:scale-115',
      shadowColor: 'rgba(0, 255, 255, 0.4)',
    },
    hero: {
      textColor: 'text-white',
      glowColor: 'rgba(255, 255, 0, 0.8)',
      hoverScale: 'hover:scale-130',
      shadowColor: 'rgba(255, 255, 0, 0.5)',
    },
  };

  const config = sizeConfig[size];
  const variantStyle = variantConfig[variant];

  const LogoContent = () => (
    <div
      ref={logoRef}
      className={`relative flex items-center ${config.container} cursor-glow group ${variantStyle.hoverScale} transition-all duration-500 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseMove={handleMouseMove}
      style={{
        transform: isHovered 
          ? `perspective(1000px) rotateX(${mousePosition.y * 10}deg) rotateY(${mousePosition.x * 10}deg) translateZ(20px)`
          : 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)',
      }}
    >
      {/* Enhanced Holographic Background Glow for Cursive Logo */}
      <div
        className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl"
        style={{
          background: `radial-gradient(circle at ${50 + mousePosition.x * 50}% ${50 + mousePosition.y * 50}%, ${variantStyle.glowColor} 0%, ${variantStyle.shadowColor} 40%, transparent 70%)`,
          transform: `scale(${1.5 + Math.sin(time) * 0.3})`,
        }}
      />

      {/* Additional cursive logo ambient glow */}
      <div
        className="absolute inset-0 rounded-2xl opacity-20 group-hover:opacity-60 transition-all duration-700 blur-2xl"
        style={{
          background: `conic-gradient(from ${time * 40}deg, ${variantStyle.glowColor}, ${variantStyle.shadowColor}, ${variantStyle.glowColor})`,
          transform: `scale(${1.8 + Math.sin(time * 0.8) * 0.2})`,
        }}
      />

      {/* Animated Border Ring */}
      <div
        className="absolute inset-0 rounded-2xl border-2 border-transparent opacity-0 group-hover:opacity-100 transition-all duration-500"
        style={{
          borderImage: `conic-gradient(from ${time * 50}deg, rgba(255, 0, 128, 0.8), rgba(0, 255, 255, 0.8), rgba(255, 255, 0, 0.8), rgba(255, 0, 128, 0.8)) 1`,
        }}
      />

      {/* Logo Container with Advanced Effects */}
      <div className="relative group">
        {/* Energy Field - Only for non-nav variants */}
        {variant !== 'nav' && (
          <div
            className="absolute inset-0 rounded-xl opacity-30 group-hover:opacity-60 transition-all duration-500"
            style={{
              background: `conic-gradient(from ${time * 30}deg, rgba(255, 0, 128, 0.3), rgba(0, 255, 255, 0.3), rgba(255, 255, 0, 0.3), rgba(255, 0, 128, 0.3))`,
              filter: 'blur(8px)',
              transform: `scale(${1.2 + Math.sin(time * 2) * 0.1})`,
            }}
          />
        )}

        {/* Main Logo */}
        <div className={`relative ${config.logo} ${variant === 'nav' ? 'rounded-lg' : 'rounded-xl overflow-hidden glass-ultra neuro-dynamic'} group-hover:scale-110 transition-all duration-500`}>
          {/* Scanning Line Effect - Only for non-nav variants */}
          {variant !== 'nav' && (
            <div
              className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-neon-cyan to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                animation: isHovered ? 'scan 2s ease-in-out infinite' : 'none',
              }}
            />
          )}

          {/* Cursive PNG Logo with enhanced effects */}
          <Image
            src="/images/logo_cursive_white.png"
            alt="Conscious Collectiv"
            fill
            className={`object-contain ${variant === 'nav' ? 'p-1' : 'p-2'} group-hover:brightness-125 transition-all duration-500`}
            style={{
              filter: variant === 'nav'
                ? 'saturate(110%) contrast(105%) drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))'
                : isHovered
                  ? `hue-rotate(${time * 30}deg) saturate(140%) contrast(120%) brightness(115%) drop-shadow(0 0 20px rgba(255, 0, 128, 0.6)) drop-shadow(0 0 40px rgba(0, 255, 255, 0.4))`
                  : 'saturate(110%) contrast(105%) drop-shadow(0 0 15px rgba(255, 255, 255, 0.2))',
              mixBlendMode: 'screen', // Remove black background from logo
              transform: isHovered
                ? `scale(${1.05 + Math.sin(time * 2) * 0.02})`
                : 'scale(1)',
            }}
          />

          {/* Enhanced Holographic Overlay */}
          <div className={`absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5 ${variant === 'nav' ? 'opacity-0 group-hover:opacity-60' : 'opacity-0 group-hover:opacity-100'} transition-opacity duration-500`} />

          {/* Cursive Logo Glow Enhancement */}
          <div
            className="absolute inset-0 opacity-40 group-hover:opacity-80 transition-all duration-500"
            style={{
              background: variant === 'nav'
                ? `radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 60%)`
                : `radial-gradient(circle at center, ${variantStyle.glowColor} 0%, transparent 70%)`,
              filter: 'blur(4px)',
              transform: `scale(${1.1 + Math.sin(time * 1.5) * 0.05})`,
            }}
          />

          {/* Pulsing Ring Effect for Cursive Logo */}
          {isHovered && (
            <div
              className="absolute inset-0 border-2 border-white/30 rounded-xl opacity-60 animate-pulse"
              style={{
                borderColor: variant === 'nav' ? 'rgba(255, 255, 255, 0.3)' : variantStyle.glowColor,
              }}
            />
          )}
        </div>

        {/* Floating Particles - Only for non-nav variants */}
        {variant !== 'nav' && isHovered && [...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-neon-cyan rounded-full animate-pulse-glow pointer-events-none"
            style={{
              left: `${20 + Math.random() * 60}%`,
              top: `${20 + Math.random() * 60}%`,
              transform: `
                translateX(${Math.sin(time + i) * 20}px)
                translateY(${Math.cos(time + i * 0.7) * 15}px)
                scale(${0.5 + Math.sin(time + i) * 0.3})
              `,
              animationDelay: `${i * 0.2}s`,
              opacity: 0.6 + Math.sin(time + i) * 0.4,
            }}
          />
        ))}
      </div>

      {/* Text Logo */}
      {showText && (
        <div className="relative">
          {/* Text Glow */}
          <div 
            className={`absolute inset-0 ${variantStyle.textColor} font-black ${config.text} tracking-tight opacity-0 group-hover:opacity-30 transition-opacity duration-500 blur-sm`}
            style={{
              transform: `scale(${1.05 + Math.sin(time) * 0.02})`,
            }}
          >
            Conscious Collectiv
          </div>

          {/* Main Text */}
          <span
            className={`relative ${variantStyle.textColor} font-black ${config.text} tracking-tight group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-neon-pink group-hover:via-neon-cyan group-hover:to-neon-yellow group-hover:animate-gradient-x transition-all duration-500`}
            style={{
              textShadow: isHovered
                ? `0 0 20px ${variantStyle.glowColor}, 0 0 40px ${variantStyle.glowColor}`
                : 'none',
            }}
          >
            Conscious Collectiv
          </span>
        </div>
      )}

      {/* Quantum Field Visualization */}
      {isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full border border-white/20 animate-ping"
              style={{
                width: `${100 + i * 50}%`,
                height: `${100 + i * 50}%`,
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                animationDelay: `${i * 0.5}s`,
                animationDuration: '2s',
              }}
            />
          ))}
        </div>
      )}
    </div>
  );

  // Wrap in Link if href is provided
  if (href) {
    return (
      <Link href={href}>
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
}
