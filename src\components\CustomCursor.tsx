import { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { simpleThrottle as throttle } from '../utils/throttle';
import { useAnimation } from '../utils/AnimationManager';

// Optimized debounce with immediate execution option
const debounce = (func: Function, delay: number, immediate = false) => {
  let timeoutId: NodeJS.Timeout;
  return function(this: any, ...args: any[]) {
    const callNow = immediate && !timeoutId;
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      timeoutId = null as any;
      if (!immediate) func.apply(this, args);
    }, delay);
    if (callNow) func.apply(this, args);
  }
};

// Lightweight particle interface for better memory efficiency
interface Particle {
  id: number;
  x: number;
  y: number;
  life: number;
  vx: number;
  vy: number;
  size: number;
  hue: number; // Use HSL hue instead of full color string
  type: 'trail' | 'burst' | 'magnetic';
}

interface MagneticField {
  x: number;
  y: number;
  strength: number;
  radius: number;
}

// Performance constants
const MAX_PARTICLES = 15; // Reduced from 30
const PARTICLE_UPDATE_INTERVAL = 32; // 30fps instead of 60fps
const MOUSE_THROTTLE = 8; // ~120fps for smooth cursor movement
const MAGNETIC_UPDATE_INTERVAL = 150; // Reduced frequency

export default function CustomCursor() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [cursorType, setCursorType] = useState('default');
  const [velocity, setVelocity] = useState({ x: 0, y: 0 });
  const [particles, setParticles] = useState<Particle[]>([]);
  const [magneticFields, setMagneticFields] = useState<MagneticField[]>([]);
  const [isClicking, setIsClicking] = useState(false);
  const [trailHistory, setTrailHistory] = useState<Array<{x: number, y: number, timestamp: number}>>([]);
  const [predictedPosition, setPredictedPosition] = useState({ x: 0, y: 0 });

  // Optimized refs for performance
  const lastPosition = useRef({ x: 0, y: 0 });
  const lastTime = useRef(Date.now());
  const particleIntervalRef = useRef<NodeJS.Timeout>();
  const magneticIntervalRef = useRef<NodeJS.Timeout>();
  const isVisible = useRef(true);
  const velocitySmoothing = useRef({ x: 0, y: 0 });

  // Use centralized animation system
  const [time, setTime] = useState(0);
  useAnimation('custom-cursor', (currentTime) => {
    if (isVisible.current) {
      setTime(currentTime);
    }
  }, 0); // High priority for cursor responsiveness

  // Memoized cursor configurations for better performance
  const cursorConfigs = useMemo(() => ({
    default: {
      size: 4,
      color: 'from-white to-gray-300',
      scale: 1,
      glow: 'from-white/20 to-gray-300/20',
      morphing: false
    },
    button: {
      size: 6,
      color: 'from-neon-yellow to-neon-orange',
      scale: 2.5,
      glow: 'from-neon-yellow/40 to-neon-orange/40',
      morphing: true
    },
    link: {
      size: 5,
      color: 'from-neon-cyan to-neon-green',
      scale: 2.2,
      glow: 'from-neon-cyan/35 to-neon-green/35',
      morphing: true
    },
    text: {
      size: 2,
      color: 'from-white to-gray-300',
      scale: 1.5,
      glow: 'from-white/20 to-gray-300/20',
      morphing: false
    },
    glow: {
      size: 8,
      color: 'from-neon-pink to-neon-purple',
      scale: 3,
      glow: 'from-neon-pink/50 to-neon-purple/50',
      morphing: true
    },
    glass: {
      size: 6,
      color: 'from-white/60 to-cyan-200/60',
      scale: 2.8,
      glow: 'from-white/30 to-cyan-200/30',
      morphing: true
    },
    hover: {
      size: 5,
      color: 'from-neon-cyan to-neon-pink',
      scale: 2,
      glow: 'from-neon-cyan/30 to-neon-pink/30',
      morphing: true
    }
  }), []);

  useEffect(() => {
    // Optimized mouse position update with throttling
    const updateMousePosition = throttle((e: MouseEvent) => {
      if (!isVisible.current) return; // Skip if not visible

      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime.current;

      // Calculate velocity for predictive movement
      const deltaX = e.clientX - lastPosition.current.x;
      const deltaY = e.clientY - lastPosition.current.y;
      const velocityX = deltaX / (deltaTime || 1);
      const velocityY = deltaY / (deltaTime || 1);

      setVelocity({ x: velocityX, y: velocityY });
      setMousePosition({ x: e.clientX, y: e.clientY });

      // Update trail history (further reduced frequency)
      if (currentTime - lastTime.current > 50) { // Only update every 50ms
        setTrailHistory(prev => [
          ...prev.slice(-8), // Reduced from 15 to 8
          { x: e.clientX, y: e.clientY, timestamp: currentTime }
        ]);
      }

      // Predict future position based on velocity
      const prediction = {
        x: e.clientX + velocityX * 30, // Reduced from 50 to 30
        y: e.clientY + velocityY * 30
      };
      setPredictedPosition(prediction);

      // Ultra-optimized particle system - only create particles on significant movement
      const speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);

      // Create velocity-based trail particles (ultra-conservative)
      if (speed > 2.0 && particles.length < MAX_PARTICLES && currentTime - lastTime.current > 32) {
        const particleCount = Math.min(Math.floor(speed * 0.3), 1); // Maximum 1 particle per update
        const newParticles: Particle[] = [];

        for (let i = 0; i < particleCount; i++) {
          const angle = Math.atan2(velocityY, velocityX) + (Math.random() - 0.5) * 0.2;
          const particleSpeed = speed * (0.2 + Math.random() * 0.2);

          newParticles.push({
            id: Date.now() + Math.random() + i,
            x: e.clientX + (Math.random() - 0.5) * 3,
            y: e.clientY + (Math.random() - 0.5) * 3,
            life: 0.6, // Even shorter life
            vx: Math.cos(angle) * particleSpeed * 0.03,
            vy: Math.sin(angle) * particleSpeed * 0.03,
            size: 1 + Math.random() * 1,
            hue: (speed * 30 + time * 15) % 360, // Use hue instead of full color string
            type: 'trail'
          });
        }

        setParticles(prev => [...prev.slice(-10), ...newParticles]); // Even smaller limit
      }

      lastPosition.current = { x: e.clientX, y: e.clientY };
      lastTime.current = currentTime;
    }, 32); // Reduced to 30fps

    // Optimized event handlers
    const handleMouseEnter = (e: Event) => {
      setIsHovering(true);
      const target = e.target as HTMLElement;

      // Element-aware cursor transformations
      if (target.tagName === 'BUTTON') {
        setCursorType('button');
      } else if (target.tagName === 'A') {
        setCursorType('link');
      } else if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        setCursorType('text');
      } else if (target.classList.contains('cursor-glow')) {
        setCursorType('glow');
      } else if (target.classList.contains('glass')) {
        setCursorType('glass');
      } else {
        setCursorType('hover');
      }
    };

    const handleMouseLeave = () => {
      setIsHovering(false);
      setCursorType('default');
    };

    const handleMouseDown = (e: MouseEvent) => {
      setIsClicking(true);

      // Create burst particles on click (much reduced count)
      if (particles.length < 10) { // Limit total particles
        const burstParticles: Particle[] = [];
        for (let i = 0; i < 4; i++) { // Reduced from 8 to 4
          const angle = (i / 4) * Math.PI * 2;
          const speed = 1.5 + Math.random() * 2;
          burstParticles.push({
            id: Date.now() + Math.random() + i,
            x: e.clientX,
            y: e.clientY,
            life: 0.8, // Shorter life
            vx: Math.cos(angle) * speed,
            vy: Math.sin(angle) * speed,
            size: 1.5 + Math.random() * 2,
            hue: Math.random() * 360,
            type: 'burst'
          });
        }
        setParticles(prev => [...prev.slice(-6), ...burstParticles]); // Keep total low
      }
    };

    const handleMouseUp = () => {
      setIsClicking(false);
    };

    // Ultra-optimized particle system with batched updates
    const particleUpdateInterval = setInterval(() => {
      if (!isVisible.current) return; // Skip when not visible

      setParticles(prev => {
        if (prev.length === 0) return prev; // Skip if no particles

        // Batch process particles for better performance
        const updatedParticles = [];
        for (let i = 0; i < prev.length; i++) {
          const particle = prev[i];

          // Simplified physics calculations
          const friction = particle.type === 'burst' ? 0.9 : 0.93;
          const newVx = particle.vx * friction;
          const newVy = particle.type === 'burst' ? particle.vy * friction + 0.06 : particle.vy * friction;

          const newLife = particle.life - (particle.type === 'burst' ? 0.1 : 0.15);
          const newSize = particle.size * (particle.type === 'burst' ? 0.94 : 0.91);

          // Only keep particles that are still visible
          if (newLife > 0 && newSize > 0.3) {
            updatedParticles.push({
              ...particle,
              x: particle.x + newVx,
              y: particle.y + newVy,
              vx: newVx,
              vy: newVy,
              life: newLife,
              size: newSize
            });
          }
        }

        return updatedParticles;
      });
    }, PARTICLE_UPDATE_INTERVAL); // Use optimized constant

    // FIXED: Store reference for proper cleanup
    particleIntervalRef.current = particleUpdateInterval;

    // Ultra-optimized magnetic field detection with caching
    const updateMagneticFields = debounce(() => {
      const fields: MagneticField[] = [];
      const magneticElements = document.querySelectorAll('.micro-haptic, .glass-interactive, .neuro-dynamic');

      // Use for loop for better performance than forEach
      for (let i = 0; i < magneticElements.length; i++) {
        const el = magneticElements[i];
        const rect = el.getBoundingClientRect();

        // Only add fields for visible elements
        if (rect.width > 0 && rect.height > 0) {
          const centerX = rect.left + rect.width * 0.5;
          const centerY = rect.top + rect.height * 0.5;
          const size = Math.max(rect.width, rect.height);

          fields.push({
            x: centerX,
            y: centerY,
            strength: size * 0.08, // Reduced strength for better performance
            radius: size * 0.6 // Reduced radius
          });
        }
      }

      setMagneticFields(fields);
    }, MAGNETIC_UPDATE_INTERVAL); // Use optimized constant

    // Update magnetic fields less frequently
    const magneticUpdateInterval = setInterval(updateMagneticFields, 2000); // Further increased to 2000ms
    magneticIntervalRef.current = magneticUpdateInterval;
    updateMagneticFields(); // Initial update

    // Visibility API for performance optimization
    const handleVisibilityChange = () => {
      isVisible.current = !document.hidden;
      if (document.hidden) {
        // Pause particle intervals when tab is not visible
        if (particleIntervalRef.current) {
          clearInterval(particleIntervalRef.current);
        }
      }
    };

    // Add event listeners
    window.addEventListener('mousemove', updateMousePosition, { passive: true });
    window.addEventListener('mousedown', handleMouseDown, { passive: true });
    window.addEventListener('mouseup', handleMouseUp, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // FIXED: Add hover listeners to interactive elements with proper cleanup
    const interactiveElements = document.querySelectorAll('a, button, input, textarea, .cursor-glow, .glass, .neuro, .micro-haptic');
    const elementsArray = Array.from(interactiveElements); // Convert to array for cleanup

    elementsArray.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter, { passive: true });
      el.addEventListener('mouseleave', handleMouseLeave, { passive: true });
    });

    return () => {
      // Remove window event listeners
      window.removeEventListener('mousemove', updateMousePosition);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      // FIXED: Proper interval cleanup
      if (particleIntervalRef.current) {
        clearInterval(particleIntervalRef.current);
        particleIntervalRef.current = undefined;
      }
      if (magneticIntervalRef.current) {
        clearInterval(magneticIntervalRef.current);
        magneticIntervalRef.current = undefined;
      }

      // FIXED: Use stored array reference for cleanup
      elementsArray.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Calculate magnetic field influence on cursor position
  const getMagneticInfluence = () => {
    let totalInfluenceX = 0;
    let totalInfluenceY = 0;

    magneticFields.forEach(field => {
      const dx = field.x - mousePosition.x;
      const dy = field.y - mousePosition.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < field.radius) {
        const influence = (1 - distance / field.radius) * field.strength;
        totalInfluenceX += (dx / distance) * influence * 0.3;
        totalInfluenceY += (dy / distance) * influence * 0.3;
      }
    });

    return { x: totalInfluenceX, y: totalInfluenceY };
  };

  const magneticInfluence = getMagneticInfluence();
  const magneticPosition = {
    x: mousePosition.x + magneticInfluence.x,
    y: mousePosition.y + magneticInfluence.y
  };

  // Optimized cursor config using memoized configurations
  const getCursorConfig = useCallback(() => {
    const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);
    const baseConfig = cursorConfigs[cursorType] || cursorConfigs.default;

    // Apply speed-based size adjustment
    const sizeMultiplier = 1 + Math.min(speed * 0.1, 1); // Cap the speed effect

    return {
      ...baseConfig,
      size: baseConfig.size * sizeMultiplier,
      scale: isHovering ? baseConfig.scale + Math.sin(time * 2) * 0.1 : 1,
    };
  }, [cursorType, velocity.x, velocity.y, isHovering, time, cursorConfigs]);
  const config = getCursorConfig();

  return (
    <>
      {/* Ultra-Optimized Particle System */}
      {particles.map(particle => {
        const size = particle.size * 2;
        const hslColor = `hsl(${particle.hue}, 70%, 50%)`;

        return (
          <div
            key={particle.id}
            className="fixed rounded-full pointer-events-none z-[9996]"
            style={{
              left: particle.x - particle.size,
              top: particle.y - particle.size,
              width: size,
              height: size,
              background: particle.type === 'burst'
                ? `radial-gradient(circle, ${hslColor} 0%, transparent 70%)`
                : `linear-gradient(45deg, ${hslColor}, transparent)`,
              opacity: particle.life * (particle.type === 'burst' ? 0.8 : 0.5),
              transform: `scale(${particle.life})`,
              filter: particle.type === 'burst' ? `blur(${(1 - particle.life)}px)` : 'none',
            }}
          />
        );
      })}

      {/* Velocity Trail History */}
      {trailHistory.slice(-10).map((point, index) => {
        const age = (Date.now() - point.timestamp) / 1000;
        const opacity = Math.max(0, 1 - age * 2);
        return (
          <div
            key={`trail-${index}`}
            className="fixed w-1 h-1 rounded-full pointer-events-none z-[9995]"
            style={{
              left: point.x - 2,
              top: point.y - 2,
              background: `hsl(${(time * 100 + index * 30) % 360}, 70%, 60%)`,
              opacity: opacity * 0.4,
              transform: `scale(${opacity})`,
              filter: `blur(${age}px)`,
            }}
          />
        );
      })}

      {/* Magnetic Field Visualization */}
      {magneticFields.map((field, index) => {
        const distance = Math.sqrt(
          Math.pow(field.x - mousePosition.x, 2) +
          Math.pow(field.y - mousePosition.y, 2)
        );
        const isNear = distance < field.radius;

        return isNear ? (
          <div
            key={`field-${index}`}
            className="fixed rounded-full pointer-events-none z-[9994]"
            style={{
              left: field.x - field.radius,
              top: field.y - field.radius,
              width: field.radius * 2,
              height: field.radius * 2,
              background: `radial-gradient(circle, transparent 60%, rgba(0, 255, 255, 0.1) 80%, transparent 100%)`,
              opacity: (1 - distance / field.radius) * 0.3,
              transform: `scale(${1 + Math.sin(time * 2) * 0.1})`,
              animation: 'pulse 2s ease-in-out infinite',
            }}
          />
        ) : null;
      })}

      {/* Predictive Cursor with Enhanced Visualization */}
      {(Math.abs(velocity.x) > 0.3 || Math.abs(velocity.y) > 0.3) && (
        <div
          className="fixed pointer-events-none z-[9995]"
          style={{
            left: predictedPosition.x - 8,
            top: predictedPosition.y - 8,
            width: 16,
            height: 16,
            opacity: Math.min(Math.abs(velocity.x) + Math.abs(velocity.y), 1) * 0.6,
          }}
        >
          <div
            className="w-full h-full rounded-full"
            style={{
              background: `conic-gradient(from ${time * 100}deg,
                rgba(0, 255, 255, 0.6) 0deg,
                rgba(255, 0, 128, 0.6) 120deg,
                rgba(255, 255, 0, 0.6) 240deg,
                rgba(0, 255, 255, 0.6) 360deg
              )`,
              transform: `rotate(${time * 50}deg) scale(${1 + Math.sin(time * 4) * 0.2})`,
              filter: 'blur(1px)',
            }}
          />
        </div>
      )}

      {/* Ultra Advanced Main Cursor */}
      <div
        className="fixed pointer-events-none z-[9999] transition-all duration-200"
        style={{
          left: magneticPosition.x - config.size * 2,
          top: magneticPosition.y - config.size * 2,
          transform: `scale(${config.scale * (isClicking ? 0.8 : 1)}) rotate(${velocity.x * 15 + time * 20}deg)`,
        }}
      >
        {/* Core Cursor with Morphing Shape */}
        <div
          className="relative"
          style={{
            width: config.size * 4,
            height: config.size * 4,
          }}
        >
          {/* Primary Gradient Core */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: config.morphing
                ? `conic-gradient(from ${time * 100}deg,
                    hsl(${(time * 50) % 360}, 80%, 60%) 0deg,
                    hsl(${(time * 50 + 120) % 360}, 80%, 60%) 120deg,
                    hsl(${(time * 50 + 240) % 360}, 80%, 60%) 240deg,
                    hsl(${(time * 50) % 360}, 80%, 60%) 360deg
                  )`
                : `linear-gradient(45deg,
                    hsl(${(time * 30) % 360}, 70%, 60%),
                    hsl(${(time * 30 + 180) % 360}, 70%, 60%)
                  )`,
              filter: `blur(${Math.abs(velocity.x + velocity.y) * 0.5}px) saturate(150%)`,
              transform: `scale(${1 + Math.sin(time * 3) * 0.1})`,
              mixBlendMode: 'screen',
            }}
          />

          {/* Velocity Distortion Layer */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: `radial-gradient(ellipse ${100 + Math.abs(velocity.x) * 50}% ${100 + Math.abs(velocity.y) * 50}% at 50% 50%,
                rgba(255, 255, 255, 0.8) 0%,
                transparent 60%
              )`,
              transform: `skew(${velocity.x * 5}deg, ${velocity.y * 5}deg)`,
              opacity: Math.min(Math.abs(velocity.x) + Math.abs(velocity.y), 1) * 0.6,
            }}
          />

          {/* Click Ripple Effect */}
          {isClicking && (
            <div
              className="absolute inset-0 rounded-full"
              style={{
                background: 'radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)',
                animation: 'ping 0.5s cubic-bezier(0, 0, 0.2, 1)',
                transform: 'scale(2)',
              }}
            />
          )}
        </div>
      </div>

      {/* Liquid Trailing Ring */}
      <div
        className="fixed pointer-events-none z-[9998] transition-all duration-500"
        style={{
          left: mousePosition.x - 20,
          top: mousePosition.y - 20,
          width: 40,
          height: 40,
        }}
      >
        <div
          className="w-full h-full rounded-full border-2"
          style={{
            borderColor: config.morphing
              ? `hsl(${(time * 80) % 360}, 70%, 60%)`
              : 'rgba(0, 255, 255, 0.6)',
            transform: `
              scale(${isHovering ? config.scale * 0.8 : 1})
              rotate(${-time * 30}deg)
              skew(${velocity.x * 3}deg, ${velocity.y * 3}deg)
            `,
            opacity: isHovering ? 0.8 : 0.3,
            filter: `blur(${Math.abs(velocity.x + velocity.y) * 0.3}px)`,
            background: isHovering
              ? `conic-gradient(from ${time * 150}deg,
                  transparent 0deg,
                  rgba(0, 255, 255, 0.2) 90deg,
                  transparent 180deg,
                  rgba(255, 0, 128, 0.2) 270deg,
                  transparent 360deg
                )`
              : 'transparent',
          }}
        />
      </div>

      {/* Multi-Layer Glow System */}
      {/* Outer Glow - Largest */}
      <div
        className="fixed rounded-full pointer-events-none z-[9996] blur-3xl transition-all duration-700"
        style={{
          width: 200 + Math.abs(velocity.x) * 30,
          height: 200 + Math.abs(velocity.y) * 30,
          left: mousePosition.x - 100 - (Math.abs(velocity.x) * 15),
          top: mousePosition.y - 100 - (Math.abs(velocity.y) * 15),
          background: config.morphing
            ? `radial-gradient(circle,
                hsl(${(time * 60) % 360}, 70%, 50%) 0%,
                hsl(${(time * 60 + 120) % 360}, 70%, 50%) 30%,
                transparent 70%
              )`
            : `radial-gradient(circle, rgba(255, 0, 128, 0.3) 0%, rgba(0, 255, 255, 0.2) 50%, transparent 100%)`,
          opacity: isHovering ? 0.6 : 0.2,
          transform: `scale(${isHovering ? 2 : 1}) rotate(${time * 10}deg)`,
        }}
      />

      {/* Middle Glow */}
      <div
        className="fixed rounded-full pointer-events-none z-[9997] blur-2xl transition-all duration-500"
        style={{
          width: 120 + Math.abs(velocity.x) * 20,
          height: 120 + Math.abs(velocity.y) * 20,
          left: mousePosition.x - 60 - (Math.abs(velocity.x) * 10),
          top: mousePosition.y - 60 - (Math.abs(velocity.y) * 10),
          background: `radial-gradient(circle,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(0, 255, 255, 0.3) 40%,
            transparent 80%
          )`,
          opacity: isHovering ? 0.8 : 0.4,
          transform: `scale(${isHovering ? 1.5 : 1}) rotate(${-time * 15}deg)`,
        }}
      />

      {/* Velocity Streak Effects */}
      {(Math.abs(velocity.x) > 0.8 || Math.abs(velocity.y) > 0.8) && (
        <>
          {/* Horizontal Streak */}
          <div
            className="fixed pointer-events-none z-[9995] blur-sm"
            style={{
              width: Math.abs(velocity.x) * 80,
              height: 3,
              left: mousePosition.x - (Math.abs(velocity.x) * 40),
              top: mousePosition.y - 1.5,
              background: `linear-gradient(90deg,
                transparent 0%,
                hsl(${(time * 100) % 360}, 80%, 60%) 50%,
                transparent 100%
              )`,
              transform: `rotate(${Math.atan2(velocity.y, velocity.x) * 180 / Math.PI}deg)`,
              opacity: Math.min(Math.abs(velocity.x), 1) * 0.8,
            }}
          />

          {/* Vertical Streak */}
          <div
            className="fixed pointer-events-none z-[9995] blur-sm"
            style={{
              width: 3,
              height: Math.abs(velocity.y) * 80,
              left: mousePosition.x - 1.5,
              top: mousePosition.y - (Math.abs(velocity.y) * 40),
              background: `linear-gradient(0deg,
                transparent 0%,
                hsl(${(time * 120 + 180) % 360}, 80%, 60%) 50%,
                transparent 100%
              )`,
              transform: `rotate(${Math.atan2(velocity.y, velocity.x) * 180 / Math.PI + 90}deg)`,
              opacity: Math.min(Math.abs(velocity.y), 1) * 0.8,
            }}
          />
        </>
      )}

      {/* Contextual Aura for Different Elements */}
      {isHovering && (
        <div
          className="fixed rounded-full pointer-events-none z-[9994] blur-xl transition-all duration-300"
          style={{
            width: 300,
            height: 300,
            left: mousePosition.x - 150,
            top: mousePosition.y - 150,
            background: cursorType === 'button'
              ? 'radial-gradient(circle, rgba(255, 255, 0, 0.2) 0%, transparent 60%)'
              : cursorType === 'link'
              ? 'radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 60%)'
              : cursorType === 'glass'
              ? 'radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 60%)'
              : 'radial-gradient(circle, rgba(255, 0, 128, 0.2) 0%, transparent 60%)',
            opacity: 0.6,
            transform: `scale(${1 + Math.sin(time * 2) * 0.1}) rotate(${time * 5}deg)`,
          }}
        />
      )}
    </>
  );
}
