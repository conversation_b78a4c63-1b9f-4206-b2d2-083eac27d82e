/**
 * Performance Optimization Utilities for About Page
 * Ensures smooth 60+ FPS performance across all animations and interactions
 */

// Throttle function for scroll and mouse events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Debounce function for resize events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

// Optimized intersection observer with performance settings
export function createOptimizedObserver(
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverInit
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    threshold: 0.1,
    rootMargin: '50px 0px', // Preload animations slightly before visible
    ...options,
  };

  return new IntersectionObserver(callback, defaultOptions);
}

// Performance-aware animation frame manager
export class AnimationFrameManager {
  private callbacks: Set<() => void> = new Set();
  private isRunning = false;
  private frameId: number | null = null;
  private lastTime = 0;
  private targetFPS = 60;
  private frameInterval = 1000 / this.targetFPS;

  add(callback: () => void): void {
    this.callbacks.add(callback);
    if (!this.isRunning) {
      this.start();
    }
  }

  remove(callback: () => void): void {
    this.callbacks.delete(callback);
    if (this.callbacks.size === 0) {
      this.stop();
    }
  }

  private start(): void {
    this.isRunning = true;
    this.lastTime = performance.now();
    this.tick();
  }

  private stop(): void {
    this.isRunning = false;
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
    }
  }

  private tick = (): void => {
    if (!this.isRunning) return;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastTime;

    // Only run callbacks if enough time has passed (FPS limiting)
    if (deltaTime >= this.frameInterval) {
      this.callbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.warn('Animation callback error:', error);
        }
      });
      this.lastTime = currentTime - (deltaTime % this.frameInterval);
    }

    this.frameId = requestAnimationFrame(this.tick);
  };
}

// Singleton instance
export const animationFrameManager = new AnimationFrameManager();

// Lazy loading utility for images and videos
export function createLazyLoader(): IntersectionObserver {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLElement;
          
          // Handle images
          if (target.tagName === 'IMG') {
            const img = target as HTMLImageElement;
            const src = img.dataset.src;
            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
            }
          }
          
          // Handle videos
          if (target.tagName === 'VIDEO') {
            const video = target as HTMLVideoElement;
            const src = video.dataset.src;
            if (src) {
              video.src = src;
              video.removeAttribute('data-src');
            }
          }
          
          // Handle background images
          const bgSrc = target.dataset.bgSrc;
          if (bgSrc) {
            target.style.backgroundImage = `url(${bgSrc})`;
            target.removeAttribute('data-bg-src');
          }
          
          // Stop observing once loaded
          this.unobserve(target);
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: '100px 0px', // Load images 100px before they're visible
    }
  );
}

// Performance monitoring
export class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 60;
  private isMonitoring = false;

  start(): void {
    if (this.isMonitoring) return;
    this.isMonitoring = true;
    this.monitor();
  }

  stop(): void {
    this.isMonitoring = false;
  }

  getFPS(): number {
    return Math.round(this.fps);
  }

  private monitor = (): void => {
    if (!this.isMonitoring) return;

    const currentTime = performance.now();
    this.frameCount++;

    // Calculate FPS every second
    if (currentTime - this.lastTime >= 1000) {
      this.fps = (this.frameCount * 1000) / (currentTime - this.lastTime);
      this.frameCount = 0;
      this.lastTime = currentTime;

      // Warn if FPS drops below 45
      if (this.fps < 45) {
        console.warn(`Low FPS detected: ${Math.round(this.fps)}fps`);
      }
    }

    requestAnimationFrame(this.monitor);
  };
}

// Memory management for animations
export function cleanupAnimations(element: HTMLElement): void {
  // Remove all animation classes
  const animationClasses = [
    'animate-slide-up-fade',
    'animate-float-slow',
    'animate-gradient-x',
    'animate-pulse-glow',
    'animate-ping',
    'animate-bounce',
    'animate-spin',
  ];

  animationClasses.forEach(className => {
    element.classList.remove(className);
  });

  // Clear any inline animation styles
  element.style.animation = '';
  element.style.transform = '';
  element.style.transition = '';
}

// Optimized scroll handler
export function createOptimizedScrollHandler(
  callback: (scrollY: number) => void,
  throttleMs = 16 // ~60fps
): () => void {
  let ticking = false;

  const handleScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        callback(window.scrollY);
        ticking = false;
      });
      ticking = true;
    }
  };

  return throttle(handleScroll, throttleMs);
}

// Reduce motion for accessibility
export function shouldReduceMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

// Adaptive quality based on device performance
export function getDevicePerformanceLevel(): 'low' | 'medium' | 'high' {
  if (typeof window === 'undefined') return 'medium';

  try {
    // Simple heuristic based on device capabilities
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext | null;

    if (!gl) return 'low';

    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (!debugInfo) return 'medium';

    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);

    // Basic GPU detection (this is a simplified approach)
    if (renderer.includes('Intel') && !renderer.includes('Iris')) {
      return 'low';
    } else if (renderer.includes('NVIDIA') || renderer.includes('AMD') || renderer.includes('Iris')) {
      return 'high';
    }

    return 'medium';
  } catch (error) {
    return 'medium';
  }
}

// Export performance monitor instance
export const performanceMonitor = new PerformanceMonitor();
