'use client';

import { useState, useEffect, useRef } from 'react';
import HyperBadge from '../HyperBadge';
import ImagePlaceholder from '../ImagePlaceholder';
import { useAnimation } from '../../utils/AnimationManager';

export default function TeamSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFounder, setActiveFounder] = useState(0);
  const [time, setTime] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  useAnimation('team-section', (currentTime) => {
    setTime(currentTime);
  }, 8);

  // Founding members data - updated with actual names
  const foundingMembers = [
    {
      id: 'founder-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Founding Member',
      bio: 'Passionate about creating transformative experiences that bridge consciousness and celebration. Background in [field] with [X] years of experience.',
      expertise: ['Creative Direction', 'Event Curation', 'Community Building'],
      color: 'from-neon-pink to-neon-purple',
      icon: '✨',
      quote: 'Dance floors are sacred spaces where souls connect.',
      achievements: ['Achievement 1', 'Achievement 2', 'Achievement 3']
    },
    {
      id: 'founder-2',
      name: 'Suresh Yarlagadda',
      email: '<EMAIL>',
      role: 'Founding Member',
      bio: 'Expert in scaling conscious businesses and building sustainable event operations. Brings [X] years of experience in [field].',
      expertise: ['Operations', 'Business Strategy', 'Sustainability'],
      color: 'from-neon-cyan to-neon-blue',
      icon: '🚀',
      quote: 'Building the infrastructure for conscious celebration.',
      achievements: ['Achievement 1', 'Achievement 2', 'Achievement 3']
    },
    {
      id: 'founder-3',
      name: 'Danny Kinas',
      email: '<EMAIL>',
      role: 'Founding Member',
      bio: 'Renowned DJ and music curator with deep roots in the electronic music scene. [X] years of experience crafting sonic journeys.',
      expertise: ['Music Curation', 'Artist Relations', 'Sound Design'],
      color: 'from-neon-green to-neon-yellow',
      icon: '🎵',
      quote: 'Music is the universal language of consciousness.',
      achievements: ['Achievement 1', 'Achievement 2', 'Achievement 3']
    },
    {
      id: 'founder-4',
      name: 'Vahagn Minissian',
      email: '<EMAIL>',
      role: 'Founding Member',
      bio: 'Dedicated to fostering authentic connections and building inclusive communities. Background in [field] with focus on [specialty].',
      expertise: ['Community Building', 'Wellness Integration', 'Cultural Programming'],
      color: 'from-neon-orange to-neon-red',
      icon: '🤝',
      quote: 'Community is the heartbeat of every great movement.',
      achievements: ['Achievement 1', 'Achievement 2', 'Achievement 3']
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      {
        threshold: 0.2,
        rootMargin: '50px' // Start animation slightly before element is visible
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    // Auto-rotate founders with pause on hover
    let founderTimer: NodeJS.Timeout;

    const startRotation = () => {
      founderTimer = setInterval(() => {
        setActiveFounder(prev => (prev + 1) % foundingMembers.length);
      }, 5000);
    };

    const stopRotation = () => {
      if (founderTimer) {
        clearInterval(founderTimer);
      }
    };

    // Start rotation initially
    startRotation();

    // Add hover listeners to pause rotation
    const section = sectionRef.current;
    if (section) {
      section.addEventListener('mouseenter', stopRotation);
      section.addEventListener('mouseleave', startRotation);
    }

    return () => {
      observer.disconnect();
      stopRotation();
      if (section) {
        section.removeEventListener('mouseenter', stopRotation);
        section.removeEventListener('mouseleave', startRotation);
      }
    };
  }, [foundingMembers.length]);

  return (
    <section
      ref={sectionRef}
      className="section-padding-large bg-black relative overflow-hidden pt-32 md:pt-40"
    >
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Morphing Team Energy Blobs */}
        <div
          className="absolute w-[800px] h-[800px] rounded-full opacity-10 blur-3xl"
          style={{
            background: `radial-gradient(circle, ${
              foundingMembers[activeFounder].color.includes('pink') ? 'rgba(236, 72, 153, 0.4)' :
              foundingMembers[activeFounder].color.includes('cyan') ? 'rgba(6, 182, 212, 0.4)' :
              foundingMembers[activeFounder].color.includes('green') ? 'rgba(34, 197, 94, 0.4)' :
              'rgba(251, 146, 60, 0.4)'
            } 0%, transparent 70%)`,
            left: `${15 + Math.sin(time * 0.15) * 20}%`,
            top: `${20 + Math.cos(time * 0.18) * 25}%`,
            transform: `scale(${1 + Math.sin(time * 0.2) * 0.3}) rotate(${time * 2}deg)`,
            transition: 'background 1s ease-out',
          }}
        />

        {/* Secondary Energy Blob */}
        <div
          className="absolute w-[600px] h-[600px] rounded-full opacity-8 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%)',
            right: `${10 + Math.cos(time * 0.12) * 15}%`,
            bottom: `${15 + Math.sin(time * 0.16) * 20}%`,
            transform: `scale(${1 + Math.cos(time * 0.18) * 0.2}) rotate(${-time * 1.5}deg)`,
          }}
        />

        {/* Floating Team Particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-20"
              style={{
                left: `${5 + (i * 4.5)}%`,
                top: `${10 + Math.sin(time * 0.3 + i) * 60}%`,
                transform: `translateY(${Math.sin(time * 0.15 + i) * 30}px)`,
                animationDelay: `${i * 0.1}s`,
              }}
            />
          ))}
        </div>
      </div>

      <div className="content-container relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-8">
            <HyperBadge
              type="featured"
              text="THE COLLECTIVE"
              size="lg"
            />
          </div>

          <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black text-white leading-none tracking-tight mb-6 md:mb-8">
            <span className="block mb-2">MEET THE</span>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-green animate-gradient-x">
              VISIONARIES
            </span>
          </h2>

          <p className="text-lg sm:text-xl md:text-2xl font-medium text-white/70 leading-relaxed max-w-4xl mx-auto px-4">
            The passionate humans behind collectiv&apos;s mission to transform nightlife into conscious celebration.
          </p>
        </div>

        {/* Symmetrical Founding Members Grid */}
        <div className={`transition-all duration-1000 delay-200 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          {/* Perfect 2x2 Grid for Founding Members */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-16 max-w-6xl mx-auto px-4 md:px-6 lg:px-0">
            {foundingMembers.map((founder, index) => (
              <div
                key={founder.id}
                className={`group relative cursor-pointer transition-all duration-500 ${
                  activeFounder === index
                    ? 'scale-105 z-10'
                    : 'hover:scale-102'
                }`}
                style={{
                  animationDelay: `${index * 150}ms`
                }}
                onClick={() => {
                  setActiveFounder(index);
                  if (navigator.vibrate) {
                    navigator.vibrate(50);
                  }
                }}
              >
                {/* Symmetrical Founder Card */}
                <div className={`
                  relative h-full p-8 rounded-3xl border transition-all duration-500
                  ${activeFounder === index
                    ? 'glass-ultra border-white/40 shadow-2xl'
                    : 'glass border-white/20 hover:border-white/30'
                  }
                `}>
                  {/* Active Indicator */}
                  {activeFounder === index && (
                    <div className="absolute -top-3 -right-3 z-20">
                      <div className={`w-6 h-6 rounded-full bg-gradient-to-r ${founder.color} animate-pulse-glow flex items-center justify-center`}>
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                    </div>
                  )}

                  {/* Profile Image - Traditional Card Layout */}
                  <div className="relative mb-6">
                    <div className="aspect-square w-40 mx-auto relative">
                      <ImagePlaceholder
                        aspectRatio="square"
                        className="w-full h-full rounded-2xl border-2 border-white/20 group-hover:border-white/40 transition-colors duration-300"
                        alt={`${founder.name} profile`}
                      />
                      {/* Holographic Overlay */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${founder.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-2xl`} />

                      {/* Small Icon Badge - Top Right Corner */}
                      <div className={`absolute -top-2 -right-2 w-8 h-8 rounded-xl bg-gradient-to-r ${founder.color} flex items-center justify-center text-sm group-hover:scale-110 transition-transform duration-300`}>
                        {founder.icon}
                      </div>
                    </div>
                  </div>

                  {/* Card Header - Below Image */}
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-black text-white mb-2">
                      {founder.name}
                    </h3>
                    <div className="text-white/60 text-sm font-medium uppercase tracking-wide">
                      {founder.role}
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-white/80 text-sm leading-relaxed text-center mb-6">
                    {founder.bio}
                  </p>

                  {/* Quote */}
                  <div className="glass rounded-2xl p-4 border border-white/10 mb-6">
                    <div className="text-xl text-neon-yellow mb-2">&ldquo;</div>
                    <p className="text-white/70 italic text-sm leading-relaxed">
                      {founder.quote}
                    </p>
                  </div>

                  {/* Expertise Tags */}
                  <div className="flex flex-wrap gap-2 justify-center">
                    {founder.expertise.map((skill, i) => (
                      <span
                        key={i}
                        className={`px-3 py-1 bg-gradient-to-r ${founder.color} bg-opacity-20 rounded-full text-xs font-bold text-white border border-white/20`}
                      >
                        {skill}
                      </span>
                    ))}
                  </div>

                  {/* Hover Glow Effect */}
                  <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${founder.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 pointer-events-none`} />
                </div>
              </div>
            ))}
          </div>

          {/* Team Stats Section */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 max-w-4xl mx-auto mb-16 px-4 md:px-6 lg:px-0">
            {/* Team Stats */}
            <div className="group glass-ultra rounded-2xl p-6 border border-white/20 text-center hover:border-white/40 transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-gradient-to-r from-neon-cyan to-neon-blue rounded-xl flex items-center justify-center mx-auto mb-4 text-lg group-hover:scale-110 transition-transform duration-300">
                📊
              </div>
              <div className="text-2xl font-black text-neon-cyan mb-2">4</div>
              <div className="text-white/80 text-sm font-medium">Founding Members</div>
            </div>

            {/* Experience */}
            <div className="group glass-ultra rounded-2xl p-6 border border-white/20 text-center hover:border-white/40 transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-gradient-to-r from-neon-pink to-neon-purple rounded-xl flex items-center justify-center mx-auto mb-4 text-lg group-hover:scale-110 transition-transform duration-300">
                🚀
              </div>
              <div className="text-2xl font-black text-neon-pink mb-2">50+</div>
              <div className="text-white/80 text-sm font-medium">Combined Years Experience</div>
            </div>

            {/* Vision */}
            <div className="group glass-ultra rounded-2xl p-6 border border-white/20 text-center hover:border-white/40 transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 bg-gradient-to-r from-neon-green to-neon-yellow rounded-xl flex items-center justify-center mx-auto mb-4 text-lg group-hover:scale-110 transition-transform duration-300">
                ✨
              </div>
              <div className="text-2xl font-black text-neon-green mb-2">∞</div>
              <div className="text-white/80 text-sm font-medium">Passion & Vision</div>
            </div>
          </div>
        </div>

        {/* Team Philosophy Section */}
        <div className={`text-center transition-all duration-1000 delay-400 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="glass-ultra rounded-3xl p-8 border border-white/20 max-w-4xl mx-auto">
            <div className="space-y-6">
              <p className="text-white/70 leading-relaxed text-lg">
                Each founder brings unique expertise and passion to our collective vision. Together, we&apos;re
                building more than events — we&apos;re cultivating a movement that transforms how people connect,
                celebrate, and grow through conscious experiences.
              </p>

              <div className="flex flex-wrap gap-3 justify-center">
                {['Visionary Leadership', 'Diverse Expertise', 'Shared Values', 'Collective Impact'].map((trait, i) => (
                  <span
                    key={i}
                    className="px-4 py-2 bg-gradient-to-r from-white/10 to-white/5 rounded-full text-sm font-bold text-white border border-white/20 hover:scale-105 transition-transform duration-300"
                  >
                    {trait}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className={`text-center mt-16 transition-all duration-1000 delay-600 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="glass rounded-3xl p-8 border border-white/20 max-w-2xl mx-auto">
            <h3 className="text-xl font-black text-white mb-4">
              WANT TO
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-green to-neon-yellow"> CONNECT?</span>
            </h3>
            <p className="text-white/70 mb-6 leading-relaxed">
              Interested in collaborating, partnering, or just want to say hello?
              Our founding team is always excited to connect with like-minded souls.
            </p>
            <button className="px-8 py-3 bg-gradient-to-r from-neon-pink to-neon-cyan rounded-full text-black font-black text-sm hover:scale-105 transition-transform duration-300 border border-white/20">
              REACH OUT TO THE TEAM
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
