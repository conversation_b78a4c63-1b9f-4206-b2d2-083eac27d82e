'use client';

import { useState, useEffect, useRef } from 'react';
import HyperBadge from '../HyperBadge';
import ImagePlaceholder from '../ImagePlaceholder';
import { useAnimation } from '../../utils/AnimationManager';


export default function OurStory() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeStoryPoint, setActiveStoryPoint] = useState(0);
  const [time, setTime] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  useAnimation('our-story', (currentTime) => {
    setTime(currentTime);
  }, 3);

  const storyPoints = [
    {
      year: '2024',
      title: 'The Awakening',
      description: 'Born from a vision to transform San Diego\'s nightlife scene, collectiv emerged as a response to the disconnection in modern party culture. We believed dance floors could be sacred spaces.',
      icon: '✨',
      color: 'from-neon-pink to-neon-purple'
    },
    {
      year: 'Present',
      title: 'Building Community',
      description: 'Today, we\'re curating experiences that go beyond entertainment. From sunset beach gatherings to underground warehouse parties, each event is designed to foster authentic human connection.',
      icon: '🌊',
      color: 'from-neon-cyan to-neon-blue'
    },
    {
      year: 'Future',
      title: 'The Movement',
      description: 'Our vision extends beyond San Diego. We\'re creating a blueprint for conscious celebration that will inspire communities worldwide to dance, connect, and transform together.',
      icon: '🚀',
      color: 'from-neon-green to-neon-yellow'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    // Auto-rotate story points
    const storyTimer = setInterval(() => {
      setActiveStoryPoint(prev => (prev + 1) % storyPoints.length);
    }, 5000);

    return () => {
      observer.disconnect();
      clearInterval(storyTimer);
    };
  }, [storyPoints.length]);

  return (
    <section 
      id="our-story"
      ref={sectionRef}
      className="section-padding-large bg-black relative overflow-hidden"
    >
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Morphing Energy Blobs */}
        <div
          className="absolute w-96 h-96 rounded-full opacity-10 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(0, 255, 255, 0.4) 0%, transparent 70%)',
            left: `${25 + Math.sin(time * 0.2) * 15}%`,
            top: `${40 + Math.cos(time * 0.3) * 20}%`,
            transform: `scale(${1 + Math.sin(time * 0.4) * 0.2}) rotate(${time * 5}deg)`,
          }}
        />

        <div
          className="absolute w-80 h-80 rounded-full opacity-8 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 255, 0, 0.3) 0%, transparent 70%)',
            right: `${20 + Math.cos(time * 0.25) * 10}%`,
            bottom: `${30 + Math.sin(time * 0.35) * 15}%`,
            transform: `scale(${1 + Math.cos(time * 0.3) * 0.2}) rotate(${-time * 4}deg)`,
          }}
        />

        {/* Quantum Grid */}
        <div 
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 0, 128, 0.2) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.2) 1px, transparent 1px)
            `,
            backgroundSize: '80px 80px',
          }}
        />
      </div>

      <div className="content-container relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="mb-8">
            <HyperBadge
              type="new"
              text="OUR STORY"
              size="lg"
            />
          </div>

          <h2 className="text-display-lg md:text-display-md font-black text-white leading-none tracking-tight mb-8">
            FROM VISION TO
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan via-neon-green to-neon-yellow animate-gradient-x">
              MOVEMENT
            </span>
          </h2>
        </div>

        {/* Clean Organized Grid Layout */}
        <div className="max-w-7xl mx-auto px-4 md:px-6 mb-20">

          {/* Main Story Section */}
          <div className={`mb-16 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="glass-ultra rounded-3xl p-8 md:p-12 border border-white/20 relative overflow-hidden max-w-4xl mx-auto">
              <div className="relative z-10">
                <div className="flex items-center space-x-4 mb-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-neon-pink to-neon-purple rounded-2xl flex items-center justify-center text-2xl">
                    ✨
                  </div>
                  <div>
                    <div className="text-white/60 text-sm font-medium uppercase tracking-wide mb-1">2024</div>
                    <h3 className="text-3xl md:text-4xl font-black text-white">The Origin Story</h3>
                  </div>
                </div>

                <div className="space-y-8">
                  <div className="space-y-6">
                    <p className="text-xl text-white/80 leading-relaxed">
                      Conscious Collectiv was born from a simple belief: that dance floors can be sacred spaces where souls connect,
                      consciousness expands, and communities transform.
                    </p>
                    <p className="text-white/70 leading-relaxed text-lg">
                      What started as late-night conversations about the disconnection in modern party culture became
                      a movement to reclaim celebration as a conscious practice.
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-3 justify-center">
                    {['Sacred Spaces', 'Soul Connection', 'Conscious Celebration', 'Community First'].map((tag, i) => (
                      <span key={i} className="px-6 py-3 bg-white/10 rounded-full text-sm font-medium text-white/80 border border-white/20 hover:border-white/40 hover:scale-105 transition-all duration-300">
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Mission Statement */}
                  <div className="text-center pt-6 border-t border-white/10">
                    <div className="inline-flex items-center space-x-3 px-6 py-4 bg-gradient-to-r from-neon-cyan/10 to-neon-blue/10 rounded-2xl border border-neon-cyan/20">
                      <div className="w-12 h-12 bg-gradient-to-r from-neon-cyan to-neon-blue rounded-xl flex items-center justify-center text-xl">
                        🎭
                      </div>
                      <div className="text-left">
                        <div className="text-white font-bold text-sm uppercase tracking-wide">Mission</div>
                        <p className="text-white/70 text-sm">Community building through conscious events</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats & Philosophy Grid */}
          <div className={`grid md:grid-cols-2 lg:grid-cols-3 gap-6 transition-all duration-1000 delay-200 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>

            {/* Impact Metrics */}
            <div className="glass-ultra rounded-3xl p-8 border border-white/20 text-center">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-white/60 text-sm font-medium uppercase tracking-wide mb-1">Growth</div>
                  <h4 className="text-2xl font-black text-white">Impact</h4>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-neon-pink to-neon-purple rounded-xl flex items-center justify-center text-xl">
                  📈
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <div className="text-4xl font-black text-neon-pink mb-2">127</div>
                  <div className="text-white/80 text-sm font-medium mb-2">Events</div>
                  <div className="w-full h-1 bg-gradient-to-r from-neon-pink to-transparent rounded-full"></div>
                </div>
                <div>
                  <div className="text-4xl font-black text-neon-cyan mb-2">8500</div>
                  <div className="text-white/80 text-sm font-medium mb-2">Community</div>
                  <div className="w-full h-1 bg-gradient-to-r from-neon-cyan to-transparent rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Philosophy */}
            <div className="glass-ultra rounded-3xl p-8 border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-white/60 text-sm font-medium uppercase tracking-wide mb-1">Core Belief</div>
                  <h4 className="text-2xl font-black text-white">Philosophy</h4>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-neon-green to-neon-yellow rounded-xl flex items-center justify-center text-xl">
                  🧘
                </div>
              </div>

              <div className="space-y-4">
                <blockquote className="text-lg text-white/80 italic leading-relaxed">
                  &ldquo;Dance floors are sacred spaces where souls connect and consciousness expands.&rdquo;
                </blockquote>
                <div className="text-white/60 text-sm">— Conscious Collectiv manifesto</div>
                <div className="w-full h-1 bg-gradient-to-r from-neon-green to-neon-yellow rounded-full"></div>
              </div>
            </div>

            {/* Vision */}
            <div className="glass-ultra rounded-3xl p-8 border border-white/20 md:col-span-2 lg:col-span-1">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-white/60 text-sm font-medium uppercase tracking-wide mb-1">Future</div>
                  <h4 className="text-2xl font-black text-white">Vision</h4>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-neon-cyan to-neon-blue rounded-xl flex items-center justify-center text-xl">
                  🌟
                </div>
              </div>

              <div className="space-y-4">
                <p className="text-white/70 leading-relaxed">
                  Global consciousness through conscious celebration
                </p>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-neon-green rounded-full animate-pulse"></div>
                  <span className="text-white/60 text-sm">Expanding worldwide</span>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </section>
  );
}
