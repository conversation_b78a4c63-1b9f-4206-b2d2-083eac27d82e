'use client';

import { useState, useEffect, useRef, memo, useCallback, useMemo } from 'react';

interface HyperBadgeProps {
  type: 'live' | 'sold-out' | 'early-bird' | 'vip' | 'featured' | 'new' | 'trending' | 'limited';
  text?: string;
  count?: number;
  maxCount?: number;
  isAnimated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const HyperBadge = memo(function HyperBadge({
  type,
  text,
  count,
  maxCount,
  isAnimated = true,
  size = 'md',
  className = '',
}: HyperBadgeProps) {
  const [time, setTime] = useState(0);
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, life: number}>>([]);
  const [isHovered, setIsHovered] = useState(false);
  const badgeRef = useRef<HTMLDivElement>(null);

  // Memoize badge configuration to prevent recreation
  const badgeConfig = useMemo(() => ({
    live: {
      colors: 'from-red-500 via-red-400 to-red-600',
      glowColor: 'rgba(239, 68, 68, 0.6)',
      icon: '●',
      defaultText: 'LIVE',
      pulseSpeed: 1.5,
      particleColor: '#ef4444'
    },
    'sold-out': {
      colors: 'from-gray-600 via-gray-500 to-gray-700',
      glowColor: 'rgba(107, 114, 128, 0.6)',
      icon: '✕',
      defaultText: 'SOLD OUT',
      pulseSpeed: 0.5,
      particleColor: '#6b7280'
    },
    'early-bird': {
      colors: 'from-yellow-400 via-yellow-300 to-orange-400',
      glowColor: 'rgba(251, 191, 36, 0.6)',
      icon: '🐦',
      defaultText: 'EARLY BIRD',
      pulseSpeed: 2,
      particleColor: '#fbbf24'
    },
    vip: {
      colors: 'from-purple-500 via-purple-400 to-pink-500',
      glowColor: 'rgba(168, 85, 247, 0.6)',
      icon: '👑',
      defaultText: 'VIP',
      pulseSpeed: 1.2,
      particleColor: '#a855f7'
    },
    featured: {
      colors: 'from-neon-pink via-neon-purple to-neon-cyan',
      glowColor: 'rgba(255, 0, 128, 0.6)',
      icon: '⭐',
      defaultText: 'FEATURED',
      pulseSpeed: 1.8,
      particleColor: '#ff0080'
    },
    new: {
      colors: 'from-green-400 via-green-300 to-emerald-400',
      glowColor: 'rgba(34, 197, 94, 0.6)',
      icon: '✨',
      defaultText: 'NEW',
      pulseSpeed: 2.5,
      particleColor: '#22c55e'
    },
    trending: {
      colors: 'from-orange-400 via-red-400 to-pink-400',
      glowColor: 'rgba(251, 146, 60, 0.6)',
      icon: '🔥',
      defaultText: 'TRENDING',
      pulseSpeed: 2.2,
      particleColor: '#fb923c'
    },
    limited: {
      colors: 'from-indigo-500 via-blue-500 to-cyan-500',
      glowColor: 'rgba(99, 102, 241, 0.6)',
      icon: '⚡',
      defaultText: 'LIMITED',
      pulseSpeed: 1.7,
      particleColor: '#6366f1'
    }
  }), []);

  const config = badgeConfig[type];
  const displayText = text || config.defaultText;

  // Memoize size classes
  const sizeClasses = useMemo(() => ({
    sm: 'px-3 py-1 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  }), []);

  // Optimize animation timer using requestAnimationFrame
  useEffect(() => {
    if (!isAnimated) return;

    let animationId: number;
    let startTime = Date.now();

    const animate = () => {
      const elapsed = (Date.now() - startTime) / 1000;
      setTime(elapsed);
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationId);
  }, [isAnimated]);

  // Optimize particle generation
  useEffect(() => {
    if (!isHovered || !isAnimated) return;

    const interval = setInterval(() => {
      const newParticle = {
        id: Date.now() + Math.random(),
        x: Math.random() * 100,
        y: Math.random() * 100,
        life: 1
      };

      setParticles(prev => [...prev.slice(-8), newParticle]); // Reduced from 10 to 8
    }, 150); // Increased from 100ms to 150ms for better performance

    return () => clearInterval(interval);
  }, [isHovered, isAnimated]);

  useEffect(() => {
    if (!isAnimated) return;

    const interval = setInterval(() => {
      setParticles(prev => 
        prev.map(particle => ({
          ...particle,
          life: particle.life - 0.02,
          y: particle.y - 1
        })).filter(particle => particle.life > 0)
      );
    }, 16);

    return () => clearInterval(interval);
  }, [isAnimated]);

  const progressPercentage = count && maxCount ? (count / maxCount) * 100 : 0;

  return (
    <div
      ref={badgeRef}
      className={`relative inline-flex items-center justify-center font-black uppercase tracking-wider rounded-full border border-white/20 backdrop-blur-sm overflow-hidden cursor-pointer group ${sizeClasses[size]} ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        background: `linear-gradient(135deg, ${config.colors.replace('from-', '').replace('via-', ', ').replace('to-', ', ')})`,
        boxShadow: isAnimated && isHovered 
          ? `0 0 30px ${config.glowColor}, 0 0 60px ${config.glowColor}` 
          : `0 0 15px ${config.glowColor}`,
        transform: isHovered ? 'scale(1.05)' : 'scale(1)',
        transition: 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
      }}
    >
      {/* Particle System */}
      {isAnimated && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-full">
          {particles.map(particle => (
            <div
              key={particle.id}
              className="absolute w-1 h-1 rounded-full"
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                backgroundColor: config.particleColor,
                opacity: particle.life,
                transform: `scale(${particle.life})`,
              }}
            />
          ))}
        </div>
      )}

      {/* Energy Pulse Background */}
      {isAnimated && (
        <div
          className="absolute inset-0 rounded-full opacity-30"
          style={{
            background: `radial-gradient(circle, ${config.particleColor} 0%, transparent 70%)`,
            transform: `scale(${1 + Math.sin(time * config.pulseSpeed) * 0.1})`,
          }}
        />
      )}

      {/* Scanning Line Effect */}
      {isAnimated && isHovered && (
        <div
          className="absolute top-0 left-0 w-full h-0.5 bg-white opacity-80"
          style={{
            animation: 'scan 1s ease-in-out infinite',
          }}
        />
      )}

      {/* Progress Bar (for count-based badges) */}
      {count !== undefined && maxCount !== undefined && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-black/30 rounded-full overflow-hidden">
          <div
            className="h-full bg-white/60 transition-all duration-500 rounded-full"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      )}

      {/* Icon */}
      <span className="mr-2 text-white drop-shadow-lg" style={{ filter: 'drop-shadow(0 0 4px rgba(0,0,0,0.5))' }}>
        {config.icon}
      </span>

      {/* Text */}
      <span className="text-white font-black drop-shadow-lg relative z-10" style={{ filter: 'drop-shadow(0 0 4px rgba(0,0,0,0.5))' }}>
        {displayText}
        {count !== undefined && ` (${count}${maxCount ? `/${maxCount}` : ''})`}
      </span>

      {/* Holographic Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />

      {/* Border Glow */}
      <div
        className="absolute inset-0 rounded-full border-2 border-white/40 opacity-0 group-hover:opacity-100 transition-all duration-300"
        style={{
          boxShadow: `inset 0 0 20px ${config.glowColor}`,
        }}
      />

      <style jsx>{`
        @keyframes scan {
          0% {
            transform: translateY(-100%);
          }
          100% {
            transform: translateY(400%);
          }
        }
      `}</style>
    </div>
  );
});

export default HyperBadge;
