import { useEffect, useState, useRef, useCallback } from 'react';
import { simpleThrottle as throttle } from '../utils/throttle';

export default function ScrollAnimations() {
  const [scrollY, setScrollY] = useState(0);
  const [bgColor, setBgColor] = useState('black');
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollVelocity, setScrollVelocity] = useState(0);
  const [inertiaOffset, setInertiaOffset] = useState(0);
  const lastScrollY = useRef(0);
  const velocityRef = useRef(0);
  const inertiaRef = useRef(0);
  const isVisible = useRef(true);
  const lastBgUpdate = useRef(0);

  // OPTIMIZED: Much more efficient scroll handler
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleScroll = useCallback(throttle(() => {
    if (!isVisible.current) return;

    const currentScrollY = window.scrollY;

    // Simplified velocity calculation
    const velocity = currentScrollY - lastScrollY.current;
    velocityRef.current = velocity;
    setScrollVelocity(velocity);
    lastScrollY.current = currentScrollY;

    // Only update if scroll difference is significant
    if (Math.abs(velocity) > 5) {
      // Much simpler inertia effect
      inertiaRef.current += velocity * 0.02; // Further reduced intensity
      inertiaRef.current *= 0.95; // More damping
      setInertiaOffset(inertiaRef.current);

      setScrollY(currentScrollY);

      // OPTIMIZED: Much less frequent background updates
      const now = Date.now();
      if (now - lastBgUpdate.current > 500) { // Throttle bg updates to 2fps
        const scrollPercent = currentScrollY / (document.body.scrollHeight - window.innerHeight);

        // Simplified background color logic
        let newBgColor = '';
        if (scrollPercent < 0.3) {
          newBgColor = 'from-black via-gray-950 to-black';
        } else if (scrollPercent < 0.7) {
          newBgColor = 'from-gray-900 via-gray-800 to-gray-700';
        } else {
          newBgColor = 'from-gray-600 via-gray-400 to-gray-300';
        }

        if (newBgColor !== bgColor) {
          setBgColor(newBgColor);
          lastBgUpdate.current = now;
        }
      }
    }
  }, 50), [bgColor]); // Reduced to 20fps for better performance

  useEffect(() => {
    // HEAVILY OPTIMIZED: Aggressive throttling with movement threshold
    const handleMouseMove = throttle((e: MouseEvent) => {
      if (!isVisible.current) return;

      const x = (e.clientX / window.innerWidth) * 2 - 1; // -1 to 1
      const y = (e.clientY / window.innerHeight) * 2 - 1; // -1 to 1

      // Only update if movement is significant (reduces global repaints)
      const threshold = 0.05; // 5% movement threshold
      if (Math.abs(x - mousePosition.x) > threshold || Math.abs(y - mousePosition.y) > threshold) {
        setMousePosition({ x, y });
      }
    }, 150); // Further reduced to ~6.7fps for optimal performance

    // Visibility API for performance optimization
    const handleVisibilityChange = () => {
      isVisible.current = !document.hidden;
    };

    // Intersection Observer for scroll reveals
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('revealed');
        }
      });
    }, observerOptions);

    // Observe all scroll-reveal elements
    const scrollElements = document.querySelectorAll('.scroll-reveal');
    scrollElements.forEach((el) => observer.observe(el));

    // Add event listeners with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('mousemove', handleMouseMove, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      observer.disconnect();
    };
  }, [handleScroll]); // Removed mousePosition from dependencies to prevent re-registration

  return (
    <>
      {/* Enhanced Dynamic Background with Parallax */}
      <div
        className={`fixed inset-0 -z-10 transition-all duration-1000 bg-gradient-to-br ${bgColor}`}
        style={{
          transform: `translateY(${scrollY * 0.1 + inertiaOffset * 0.05}px) scale(${1 + Math.abs(scrollVelocity) * 0.001})`
        }}
      />

      {/* OPTIMIZED: Reduced parallax layers for better performance */}
      <div className="fixed inset-0 pointer-events-none -z-5">
        {/* Layer 1 - Primary layer with simplified transforms */}
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-neon-pink/15 to-neon-purple/15 rounded-full blur-3xl"
          style={{
            top: '15%',
            left: '8%',
            transform: `
              translateY(${scrollY * 0.3}px)
              translateX(${mousePosition.x * 15}px)
            `
          }}
        />

        {/* Layer 2 - Secondary layer with minimal effects */}
        <div
          className="absolute w-72 h-72 bg-gradient-to-r from-neon-cyan/10 to-neon-green/10 rounded-full blur-3xl"
          style={{
            top: '55%',
            right: '12%',
            transform: `
              translateY(${scrollY * -0.2}px)
              translateX(${mousePosition.y * -10}px)
            `
          }}
        />

        {/* REMOVED: Layers 3-6 for performance optimization */}
      </div>

      {/* REMOVED: Expensive velocity-based screen distortion for performance */}
    </>
  );
}
