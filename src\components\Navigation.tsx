import { useState } from 'react';
import Link from 'next/link';
import Logo from './Logo';

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass border-b border-white/10">
      <div className="content-container">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Logo variant="nav" size="lg" showText={false} />

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <div className="flex items-center space-x-8">
              <Link
                href="/events"
                className="text-white hover:text-neon-pink transition-all duration-300 font-bold text-sm uppercase tracking-wide cursor-glow hover:scale-105"
              >
                Events
              </Link>

              <Link
                href="/experiences"
                className="text-white hover:text-neon-cyan transition-all duration-300 font-bold text-sm uppercase tracking-wide cursor-glow hover:scale-105"
              >
                Experiences
              </Link>

              <Link
                href="/venues"
                className="text-white hover:text-neon-yellow transition-all duration-300 font-bold text-sm uppercase tracking-wide cursor-glow hover:scale-105"
              >
                Venues
              </Link>

              <Link
                href="/about"
                className="text-white hover:text-neon-green transition-all duration-300 font-bold text-sm uppercase tracking-wide cursor-glow hover:scale-105"
              >
                About
              </Link>
            </div>

            {/* CTA Buttons */}
            <div className="flex items-center space-x-4">
              <Link
                href="/tickets"
                className="px-8 py-4 bg-gradient-to-r from-neon-pink to-neon-cyan text-black font-black text-sm uppercase tracking-wide rounded-full hover:scale-110 hover:rotate-1 transition-all duration-300 animate-pulse-glow cursor-glow"
              >
                Get Tickets
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="md:hidden p-2 text-text-secondary hover:text-text-primary transition-colors duration-200"
            aria-label="Toggle menu"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-neon-pink/20 bg-black/95 backdrop-blur-md">
            <div className="py-6 space-y-6">
              <div className="space-y-4">
                <Link
                  href="/events"
                  className="block text-white hover:text-neon-pink transition-colors duration-200 font-bold text-lg uppercase tracking-wide py-3"
                >
                  Events
                </Link>
                <Link
                  href="/experiences"
                  className="block text-white hover:text-neon-cyan transition-colors duration-200 font-bold text-lg uppercase tracking-wide py-3"
                >
                  Experiences
                </Link>
                <Link
                  href="/venues"
                  className="block text-white hover:text-neon-yellow transition-colors duration-200 font-bold text-lg uppercase tracking-wide py-3"
                >
                  Venues
                </Link>
                <Link
                  href="/about"
                  className="block text-white hover:text-neon-green transition-colors duration-200 font-bold text-lg uppercase tracking-wide py-3"
                >
                  About
                </Link>
              </div>

              <div className="pt-4 border-t border-neon-cyan/20">
                <Link href="/tickets" className="block w-full px-6 py-4 bg-gradient-to-r from-neon-pink to-neon-cyan text-black font-black text-lg uppercase tracking-wide rounded-full text-center">
                  Get Tickets
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
