import { ReactNode } from 'react';

interface VideoPlaceholderProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  children?: ReactNode;
  showPlayButton?: boolean;
  overlay?: boolean;
  aspectRatio?: 'video' | 'square' | 'portrait';
  alt?: string;
}

export default function VideoPlaceholder({
  width = '100%',
  height = '300px',
  className = '',
  children,
  showPlayButton = true,
  overlay = true,
  aspectRatio = 'video',
  alt = 'Video placeholder',
}: VideoPlaceholderProps) {
  const aspectRatioClasses = {
    video: 'aspect-video',
    square: 'aspect-square',
    portrait: 'aspect-[3/4]',
  };

  const aspectClass = aspectRatioClasses[aspectRatio];
  const style = aspectRatio ? {} : { width, height };

  return (
    <div
      className={`video-placeholder ${aspectClass} ${className}`}
      style={style}
      role="img"
      aria-label={alt}
    >
      {/* Background gradient overlay */}
      {overlay && <div className="absolute inset-0 bg-gradient-overlay" />}
      
      {/* Play button */}
      {showPlayButton && (
        <div className="play-icon">
          <div className="w-16 h-16 bg-black bg-opacity-40 rounded-full flex items-center justify-center text-white text-2xl hover:bg-opacity-60 transition-all duration-200 cursor-pointer">
            ▶
          </div>
        </div>
      )}

      {/* Custom content */}
      {children && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          {children}
        </div>
      )}

      {/* Default video icon if no play button */}
      {!showPlayButton && !children && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-400">
          <svg
            className="w-12 h-12"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
        </div>
      )}
    </div>
  );
}
