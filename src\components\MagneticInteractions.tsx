'use client';

import { useState, useEffect, useRef, ReactNode } from 'react';

interface MagneticElementProps {
  children: ReactNode;
  strength?: number;
  className?: string;
  disabled?: boolean;
}

export function MagneticElement({ 
  children, 
  strength = 0.3, 
  className = '', 
  disabled = false 
}: MagneticElementProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (disabled || !elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;
    
    setPosition({ x: deltaX, y: deltaY });
  };

  const handleMouseLeave = () => {
    setPosition({ x: 0, y: 0 });
    setIsHovered(false);
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  return (
    <div
      ref={elementRef}
      className={`transition-transform duration-300 ease-out ${className}`}
      style={{
        transform: `translate3d(${position.x}px, ${position.y}px, 0) ${isHovered ? 'scale(1.05)' : 'scale(1)'}`,
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
    >
      {children}
    </div>
  );
}

interface MorphingButtonProps {
  children: ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function MorphingButton({ 
  children, 
  onClick, 
  variant = 'primary', 
  size = 'md',
  className = '' 
}: MorphingButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  const variants = {
    primary: 'bg-gradient-to-r from-neon-pink to-neon-cyan text-black',
    secondary: 'bg-white/10 text-white border border-white/20',
    ghost: 'bg-transparent text-white hover:bg-white/10'
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  const handleClick = (e: React.MouseEvent) => {
    if (onClick) onClick();

    // Create ripple effect
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const newRipple = { id: Date.now(), x, y };
    
    setRipples(prev => [...prev, newRipple]);
    
    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);
  };

  return (
    <button
      className={`
        relative overflow-hidden rounded-full font-bold uppercase tracking-wide
        transition-all duration-300 ease-out
        ${variants[variant]} ${sizes[size]} ${className}
        ${isHovered ? 'shadow-2xl' : 'shadow-lg'}
        ${isPressed ? 'scale-95' : isHovered ? 'scale-105' : 'scale-100'}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onClick={handleClick}
    >
      {/* Background morphing effect */}
      <div className={`
        absolute inset-0 transition-all duration-300
        ${isHovered ? 'bg-white/20' : 'bg-transparent'}
      `} />
      
      {/* Ripple effects */}
      {ripples.map(ripple => (
        <div
          key={ripple.id}
          className="absolute bg-white/30 rounded-full animate-ping"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
          }}
        />
      ))}
      
      {/* Content */}
      <span className="relative z-10">{children}</span>
    </button>
  );
}

interface FloatingCardProps {
  children: ReactNode;
  className?: string;
  intensity?: number;
}

export function FloatingCard({ children, className = '', intensity = 1 }: FloatingCardProps) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!cardRef.current || !isHovered) return;

      const rect = cardRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) / window.innerWidth;
      const deltaY = (e.clientY - centerY) / window.innerHeight;
      
      setMousePosition({ 
        x: deltaX * 20 * intensity, 
        y: deltaY * 20 * intensity 
      });
    };

    if (isHovered) {
      window.addEventListener('mousemove', handleGlobalMouseMove);
    }

    return () => {
      window.removeEventListener('mousemove', handleGlobalMouseMove);
    };
  }, [isHovered, intensity]);

  return (
    <div
      ref={cardRef}
      className={`
        transition-all duration-500 ease-out
        ${className}
      `}
      style={{
        transform: `
          translate3d(${mousePosition.x}px, ${mousePosition.y}px, 0)
          rotateX(${mousePosition.y * 0.5}deg)
          rotateY(${mousePosition.x * 0.5}deg)
          ${isHovered ? 'scale(1.02)' : 'scale(1)'}
        `,
        transformStyle: 'preserve-3d',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setMousePosition({ x: 0, y: 0 });
      }}
    >
      {children}
      
      {/* Hover glow effect */}
      {isHovered && (
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5 rounded-3xl pointer-events-none" />
      )}
    </div>
  );
}

interface ParallaxElementProps {
  children: ReactNode;
  speed?: number;
  className?: string;
}

export function ParallaxElement({ children, speed = 0.5, className = '' }: ParallaxElementProps) {
  const [offset, setOffset] = useState(0);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!elementRef.current) return;
      
      const rect = elementRef.current.getBoundingClientRect();
      const scrolled = window.scrollY;
      const rate = scrolled * speed;
      
      setOffset(rate);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <div
      ref={elementRef}
      className={className}
      style={{
        transform: `translate3d(0, ${offset}px, 0)`,
      }}
    >
      {children}
    </div>
  );
}

interface GlitchTextProps {
  children: string;
  className?: string;
  intensity?: number;
}

export function GlitchText({ children, className = '', intensity = 1 }: GlitchTextProps) {
  const [isGlitching, setIsGlitching] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsGlitching(true);
      setTimeout(() => setIsGlitching(false), 200);
    }, 3000 + Math.random() * 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <span 
      className={`
        relative inline-block
        ${isGlitching ? 'animate-pulse' : ''}
        ${className}
      `}
      style={{
        textShadow: isGlitching ? `
          ${2 * intensity}px 0 #ff0080,
          -${2 * intensity}px 0 #00ffff,
          0 ${2 * intensity}px #ffff00
        ` : 'none',
      }}
    >
      {children}
      
      {/* Glitch overlay layers */}
      {isGlitching && (
        <>
          <span 
            className="absolute inset-0 text-neon-pink opacity-70"
            style={{
              transform: `translateX(${2 * intensity}px)`,
              clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)',
            }}
          >
            {children}
          </span>
          <span 
            className="absolute inset-0 text-neon-cyan opacity-70"
            style={{
              transform: `translateX(-${2 * intensity}px)`,
              clipPath: 'polygon(0 55%, 100% 55%, 100% 100%, 0 100%)',
            }}
          >
            {children}
          </span>
        </>
      )}
    </span>
  );
}

// Hover reveal component
export function HoverReveal({ children, className = '' }: { children: ReactNode; className?: string }) {
  const [isRevealed, setIsRevealed] = useState(false);

  return (
    <div
      className={`relative overflow-hidden ${className}`}
      onMouseEnter={() => setIsRevealed(true)}
      onMouseLeave={() => setIsRevealed(false)}
    >
      <div className={`transition-all duration-500 ${isRevealed ? 'transform-none' : 'transform translate-y-full'}`}>
        {children}
      </div>
      <div className={`absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent transition-opacity duration-300 ${isRevealed ? 'opacity-0' : 'opacity-100'}`} />
    </div>
  );
}
