'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { simpleThrottle as throttle } from '../utils/throttle';

interface QuantumParticle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  energy: number;
  phase: number;
  frequency: number;
}

interface NeuralNode {
  id: number;
  x: number;
  y: number;
  activation: number;
  connections: number[];
  pulsePhase: number;
}

interface LiquidBlob {
  x: number;
  y: number;
  radius: number;
  targetRadius: number;
  viscosity: number;
  surface: number[];
}

export default function AdvancedMouseEffects() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [velocity, setVelocity] = useState({ x: 0, y: 0 });
  const [isActive, setIsActive] = useState(false);
  const [effectMode, setEffectMode] = useState<'quantum' | 'neural' | 'liquid' | 'holographic'>('quantum');
  
  // Quantum field state
  const [quantumParticles, setQuantumParticles] = useState<QuantumParticle[]>([]);
  const [fieldIntensity, setFieldIntensity] = useState(0);
  
  // Neural network state
  const [neuralNodes, setNeuralNodes] = useState<NeuralNode[]>([]);
  const [synapseActivity, setSynapseActivity] = useState<Array<{from: number, to: number, strength: number}>>([]);
  
  // Liquid morphing state
  const [liquidBlob, setLiquidBlob] = useState<LiquidBlob>({
    x: 0, y: 0, radius: 20, targetRadius: 20, viscosity: 0.8,
    surface: Array(16).fill(0).map((_, i) => Math.sin(i * Math.PI / 8) * 5)
  });
  
  // Holographic state
  const [hologramLayers, setHologramLayers] = useState<Array<{
    x: number, y: number, rotation: number, scale: number, opacity: number, hue: number
  }>>([]);

  const lastPosition = useRef({ x: 0, y: 0 });
  const lastTime = useRef(Date.now());
  const animationRef = useRef<number>();
  const isVisible = useRef(true);

  // OPTIMIZED: Performance constants - reduced for better performance
  const MAX_QUANTUM_PARTICLES = 6; // Reduced from 12
  const MAX_NEURAL_NODES = 4; // Reduced from 8
  const MAX_HOLOGRAM_LAYERS = 3; // Reduced from 6
  const UPDATE_INTERVAL = 32; // 30fps instead of 60fps

  // Initialize neural network
  useEffect(() => {
    const nodes: NeuralNode[] = [];
    for (let i = 0; i < MAX_NEURAL_NODES; i++) {
      nodes.push({
        id: i,
        x: Math.random() * 200 - 100,
        y: Math.random() * 200 - 100,
        activation: Math.random(),
        connections: [],
        pulsePhase: Math.random() * Math.PI * 2
      });
    }
    
    // Create connections between nodes
    nodes.forEach(node => {
      const connectionCount = Math.floor(Math.random() * 3) + 1;
      for (let i = 0; i < connectionCount; i++) {
        const targetId = Math.floor(Math.random() * MAX_NEURAL_NODES);
        if (targetId !== node.id && !node.connections.includes(targetId)) {
          node.connections.push(targetId);
        }
      }
    });
    
    setNeuralNodes(nodes);
  }, []);

  // Optimized mouse tracking
  useEffect(() => {
    const updateMousePosition = throttle((e: MouseEvent) => {
      if (!isVisible.current) return;

      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime.current;
      
      const deltaX = e.clientX - lastPosition.current.x;
      const deltaY = e.clientY - lastPosition.current.y;
      const velocityX = deltaX / (deltaTime || 1);
      const velocityY = deltaY / (deltaTime || 1);

      setVelocity({ x: velocityX, y: velocityY });
      setMousePosition({ x: e.clientX, y: e.clientY });

      // Update field intensity based on movement speed
      const speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
      setFieldIntensity(Math.min(speed * 0.1, 1));

      lastPosition.current = { x: e.clientX, y: e.clientY };
      lastTime.current = currentTime;
    }, 50); // HEAVILY OPTIMIZED: Aggressive throttling for maximum performance

    const handleVisibilityChange = () => {
      isVisible.current = !document.hidden;
    };

    window.addEventListener('mousemove', updateMousePosition, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Advanced animation loop
  useEffect(() => {
    let lastUpdate = 0;

    const animate = (currentTime: number) => {
      if (!isVisible.current) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      if (currentTime - lastUpdate >= UPDATE_INTERVAL && isActive) { // Only update when active
        const time = currentTime * 0.001;

        // Update quantum particles
        if (effectMode === 'quantum') {
          setQuantumParticles(prev => {
            const updated = prev.map(particle => ({
              ...particle,
              x: particle.x + particle.vx,
              y: particle.y + particle.vy,
              vx: particle.vx * 0.98 + (Math.random() - 0.5) * 0.1,
              vy: particle.vy * 0.98 + (Math.random() - 0.5) * 0.1,
              life: particle.life - 0.01,
              phase: particle.phase + particle.frequency,
              energy: particle.energy * 0.995
            })).filter(p => p.life > 0);

            // Add new particles based on field intensity
            if (updated.length < MAX_QUANTUM_PARTICLES && fieldIntensity > 0.1) {
              const newParticle: QuantumParticle = {
                id: Date.now() + Math.random(),
                x: mousePosition.x + (Math.random() - 0.5) * 50,
                y: mousePosition.y + (Math.random() - 0.5) * 50,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                life: 1,
                energy: fieldIntensity,
                phase: Math.random() * Math.PI * 2,
                frequency: 0.1 + Math.random() * 0.1
              };
              updated.push(newParticle);
            }

            return updated;
          });
        }

        // Update neural network
        if (effectMode === 'neural') {
          setNeuralNodes(prev => prev.map(node => ({
            ...node,
            activation: Math.max(0, node.activation + (Math.random() - 0.5) * 0.1),
            pulsePhase: node.pulsePhase + 0.1
          })));

          // Update synapse activity
          setSynapseActivity(prev => {
            const newActivity = [];
            neuralNodes.forEach(node => {
              node.connections.forEach(targetId => {
                if (Math.random() < node.activation * 0.3) {
                  newActivity.push({
                    from: node.id,
                    to: targetId,
                    strength: node.activation * (0.5 + Math.random() * 0.5)
                  });
                }
              });
            });
            return newActivity;
          });
        }

        // Update liquid morphing
        if (effectMode === 'liquid') {
          setLiquidBlob(prev => {
            const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);
            const targetRadius = 20 + speed * 2;
            const newRadius = prev.radius + (targetRadius - prev.radius) * 0.1;
            
            const newSurface = prev.surface.map((point, i) => {
              const angle = (i / prev.surface.length) * Math.PI * 2;
              const noise = Math.sin(time * 3 + angle * 4) * (speed * 0.5);
              return point + (noise - point) * 0.1;
            });

            return {
              ...prev,
              x: mousePosition.x,
              y: mousePosition.y,
              radius: newRadius,
              targetRadius,
              surface: newSurface
            };
          });
        }

        // Update holographic layers
        if (effectMode === 'holographic') {
          setHologramLayers(prev => {
            const layers = [];
            for (let i = 0; i < MAX_HOLOGRAM_LAYERS; i++) {
              const delay = i * 0.1;
              const targetX = mousePosition.x;
              const targetY = mousePosition.y;
              
              const existing = prev[i];
              const x = existing ? existing.x + (targetX - existing.x) * (0.8 - delay) : targetX;
              const y = existing ? existing.y + (targetY - existing.y) * (0.8 - delay) : targetY;
              
              layers.push({
                x,
                y,
                rotation: time * (30 + i * 10),
                scale: 1 - i * 0.1 + Math.sin(time * 2 + i) * 0.05,
                opacity: (1 - i * 0.15) * fieldIntensity,
                hue: (time * 50 + i * 60) % 360
              });
            }
            return layers;
          });
        }

        lastUpdate = currentTime;
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [effectMode, mousePosition, velocity, fieldIntensity, neuralNodes, isActive]);

  // Effect mode cycling
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        e.preventDefault();
        const modes: Array<typeof effectMode> = ['quantum', 'neural', 'liquid', 'holographic'];
        const currentIndex = modes.indexOf(effectMode);
        const nextIndex = (currentIndex + 1) % modes.length;
        setEffectMode(modes[nextIndex]);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [effectMode]);

  // Memoized render functions for performance
  const renderQuantumField = useMemo(() => (
    <>
      {/* Quantum Field Background */}
      <div
        className="fixed inset-0 pointer-events-none z-[9990]"
        style={{
          background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, 
            rgba(138, 43, 226, ${fieldIntensity * 0.1}) 0%, 
            rgba(75, 0, 130, ${fieldIntensity * 0.05}) 30%, 
            transparent 60%)`,
          filter: 'blur(1px)',
        }}
      />
      
      {/* Quantum Particles */}
      {quantumParticles.map(particle => (
        <div
          key={particle.id}
          className="fixed pointer-events-none z-[9995]"
          style={{
            left: particle.x - 3,
            top: particle.y - 3,
            width: 6,
            height: 6,
            background: `hsl(${280 + particle.phase * 50}, 80%, 60%)`,
            borderRadius: '50%',
            opacity: particle.life * particle.energy,
            transform: `scale(${0.5 + Math.sin(particle.phase) * 0.3})`,
            boxShadow: `0 0 ${particle.energy * 10}px hsl(${280 + particle.phase * 50}, 80%, 60%)`,
            filter: `blur(${(1 - particle.life) * 2}px)`,
          }}
        />
      ))}
      
      {/* Quantum Entanglement Lines */}
      <svg className="fixed inset-0 pointer-events-none z-[9994]" style={{ width: '100vw', height: '100vh' }}>
        {quantumParticles.map((particle, i) => 
          quantumParticles.slice(i + 1).map((other, j) => {
            const distance = Math.sqrt(
              Math.pow(particle.x - other.x, 2) + Math.pow(particle.y - other.y, 2)
            );
            if (distance < 100) {
              const opacity = (1 - distance / 100) * particle.energy * other.energy * 0.3;
              return (
                <line
                  key={`${particle.id}-${other.id}`}
                  x1={particle.x}
                  y1={particle.y}
                  x2={other.x}
                  y2={other.y}
                  stroke={`hsl(${300 + particle.phase * 30}, 70%, 50%)`}
                  strokeWidth="1"
                  opacity={opacity}
                  strokeDasharray="2,2"
                />
              );
            }
            return null;
          })
        )}
      </svg>
    </>
  ), [quantumParticles, mousePosition, fieldIntensity]);

  return (
    <>
      {/* Effect Mode Indicator */}
      <div className="fixed top-4 right-4 z-[9999] text-white/60 text-sm font-mono">
        Mode: {effectMode.toUpperCase()} (Tab to cycle)
      </div>

      {/* Render current effect */}
      {effectMode === 'quantum' && renderQuantumField}
      
      {effectMode === 'neural' && (
        <>
          {/* Neural Network Visualization */}
          <svg className="fixed inset-0 pointer-events-none z-[9994]" style={{ width: '100vw', height: '100vh' }}>
            {/* Render connections */}
            {synapseActivity.map((synapse, i) => {
              const fromNode = neuralNodes[synapse.from];
              const toNode = neuralNodes[synapse.to];
              if (!fromNode || !toNode) return null;
              
              return (
                <line
                  key={i}
                  x1={mousePosition.x + fromNode.x}
                  y1={mousePosition.y + fromNode.y}
                  x2={mousePosition.x + toNode.x}
                  y2={mousePosition.y + toNode.y}
                  stroke={`hsl(${180 + synapse.strength * 60}, 70%, 50%)`}
                  strokeWidth={synapse.strength * 3}
                  opacity={synapse.strength * 0.8}
                />
              );
            })}
          </svg>
          
          {/* Neural Nodes */}
          {neuralNodes.map(node => (
            <div
              key={node.id}
              className="fixed pointer-events-none z-[9995]"
              style={{
                left: mousePosition.x + node.x - 4,
                top: mousePosition.y + node.y - 4,
                width: 8 + node.activation * 8,
                height: 8 + node.activation * 8,
                background: `hsl(${180 + node.activation * 60}, 80%, 60%)`,
                borderRadius: '50%',
                opacity: 0.7 + node.activation * 0.3,
                transform: `scale(${1 + Math.sin(node.pulsePhase) * node.activation * 0.3})`,
                boxShadow: `0 0 ${node.activation * 15}px hsl(${180 + node.activation * 60}, 80%, 60%)`,
              }}
            />
          ))}
        </>
      )}

      {effectMode === 'liquid' && (
        <>
          {/* Liquid Morphing Cursor */}
          <div
            className="fixed pointer-events-none z-[9995]"
            style={{
              left: liquidBlob.x - liquidBlob.radius,
              top: liquidBlob.y - liquidBlob.radius,
              width: liquidBlob.radius * 2,
              height: liquidBlob.radius * 2,
            }}
          >
            <svg width={liquidBlob.radius * 2} height={liquidBlob.radius * 2}>
              <defs>
                <filter id="gooey">
                  <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
                  <feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 20 -10"/>
                </filter>
                <radialGradient id="liquidGradient">
                  <stop offset="0%" stopColor="hsl(200, 80%, 60%)" stopOpacity="0.8"/>
                  <stop offset="70%" stopColor="hsl(280, 70%, 50%)" stopOpacity="0.6"/>
                  <stop offset="100%" stopColor="transparent"/>
                </radialGradient>
              </defs>
              <path
                d={`M ${liquidBlob.radius} ${liquidBlob.radius} ${liquidBlob.surface.map((point, i) => {
                  const angle = (i / liquidBlob.surface.length) * Math.PI * 2;
                  const x = liquidBlob.radius + Math.cos(angle) * (liquidBlob.radius + point);
                  const y = liquidBlob.radius + Math.sin(angle) * (liquidBlob.radius + point);
                  return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
                }).join(' ')} Z`}
                fill="url(#liquidGradient)"
                filter="url(#gooey)"
              />
            </svg>
          </div>

          {/* Liquid Droplets */}
          {Array.from({ length: 3 }).map((_, i) => (
            <div
              key={i}
              className="fixed pointer-events-none z-[9994]"
              style={{
                left: liquidBlob.x + (Math.random() - 0.5) * 60 - 5,
                top: liquidBlob.y + (Math.random() - 0.5) * 60 - 5,
                width: 10,
                height: 10,
                background: `hsl(${220 + i * 30}, 70%, 50%)`,
                borderRadius: '50%',
                opacity: 0.4,
                transform: `scale(${0.5 + Math.sin(Date.now() * 0.01 + i) * 0.3})`,
                filter: 'blur(1px)',
              }}
            />
          ))}
        </>
      )}

      {effectMode === 'holographic' && (
        <>
          {/* Holographic Layers */}
          {hologramLayers.map((layer, i) => (
            <div
              key={i}
              className="fixed pointer-events-none z-[9995]"
              style={{
                left: layer.x - 20,
                top: layer.y - 20,
                width: 40,
                height: 40,
                background: `conic-gradient(from ${layer.rotation}deg,
                  hsl(${layer.hue}, 80%, 60%) 0deg,
                  hsl(${layer.hue + 60}, 70%, 50%) 120deg,
                  hsl(${layer.hue + 120}, 80%, 60%) 240deg,
                  hsl(${layer.hue}, 80%, 60%) 360deg)`,
                borderRadius: '50%',
                opacity: layer.opacity,
                transform: `scale(${layer.scale}) rotate(${layer.rotation}deg)`,
                filter: `blur(${i * 0.5}px) saturate(150%)`,
                mixBlendMode: 'screen',
              }}
            />
          ))}

          {/* Holographic Interference Pattern */}
          <div
            className="fixed pointer-events-none z-[9993]"
            style={{
              left: mousePosition.x - 100,
              top: mousePosition.y - 100,
              width: 200,
              height: 200,
              background: `radial-gradient(circle,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 30%,
                transparent 60%),
                repeating-linear-gradient(
                  45deg,
                  transparent 0px,
                  rgba(255, 255, 255, 0.05) 1px,
                  transparent 2px,
                  rgba(255, 255, 255, 0.05) 3px
                )`,
              opacity: fieldIntensity * 0.6,
              transform: `rotate(${Date.now() * 0.1}deg)`,
              filter: 'blur(0.5px)',
              mixBlendMode: 'overlay',
            }}
          />

          {/* Chromatic Aberration Effect */}
          <div
            className="fixed pointer-events-none z-[9992]"
            style={{
              left: mousePosition.x - 15,
              top: mousePosition.y - 15,
              width: 30,
              height: 30,
              background: `radial-gradient(circle,
                rgba(255, 0, 0, 0.3) 0%,
                transparent 40%),
                radial-gradient(circle at 2px 2px,
                rgba(0, 255, 0, 0.3) 0%,
                transparent 40%),
                radial-gradient(circle at -2px -2px,
                rgba(0, 0, 255, 0.3) 0%,
                transparent 40%)`,
              borderRadius: '50%',
              opacity: fieldIntensity,
              filter: 'blur(1px)',
            }}
          />
        </>
      )}
    </>
  );
}
