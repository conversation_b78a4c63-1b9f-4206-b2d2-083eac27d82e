'use client';

import { useState, useEffect, useRef, ReactNode, memo, useCallback, useMemo } from 'react';

interface HyperButtonProps {
  variant: 'primary' | 'secondary' | 'ghost' | 'neon' | 'magnetic' | 'quantum' | 'plasma' | 'holographic';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  icon?: ReactNode;
  soundEnabled?: boolean;
}

const HyperButton = memo(function HyperButton({
  variant,
  size = 'md',
  children,
  onClick,
  disabled = false,
  loading = false,
  className = '',
  icon,
  soundEnabled = false,
}: HyperButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{id: number, x: number, y: number}>>([]);
  const [magneticOffset, setMagneticOffset] = useState({ x: 0, y: 0 });
  const [time, setTime] = useState(0);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Memoize variants to prevent recreation on every render
  const variants = useMemo(() => ({
    primary: {
      base: 'bg-gradient-to-r from-neon-pink to-neon-cyan text-black',
      hover: 'from-neon-cyan to-neon-pink',
      glow: 'rgba(255, 0, 128, 0.6)',
      particles: '#ff0080'
    },
    secondary: {
      base: 'bg-gradient-to-r from-gray-800 to-gray-900 text-white border border-white/20',
      hover: 'from-gray-700 to-gray-800 border-white/40',
      glow: 'rgba(255, 255, 255, 0.3)',
      particles: '#ffffff'
    },
    ghost: {
      base: 'bg-transparent text-white border border-white/30',
      hover: 'bg-white/10 border-white/60',
      glow: 'rgba(255, 255, 255, 0.4)',
      particles: '#ffffff'
    },
    neon: {
      base: 'bg-black text-neon-cyan border-2 border-neon-cyan',
      hover: 'bg-neon-cyan/10 border-neon-pink text-neon-pink',
      glow: 'rgba(0, 255, 255, 0.8)',
      particles: '#00ffff'
    },
    magnetic: {
      base: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white',
      hover: 'from-blue-600 to-purple-600',
      glow: 'rgba(147, 51, 234, 0.6)',
      particles: '#9333ea'
    },
    quantum: {
      base: 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white',
      hover: 'from-pink-500 via-purple-500 to-indigo-500',
      glow: 'rgba(168, 85, 247, 0.7)',
      particles: '#a855f7'
    },
    plasma: {
      base: 'bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white',
      hover: 'from-pink-500 via-red-500 to-orange-500',
      glow: 'rgba(239, 68, 68, 0.7)',
      particles: '#ef4444'
    },
    holographic: {
      base: 'bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 text-white',
      hover: 'from-purple-600 via-blue-500 to-cyan-400',
      glow: 'rgba(59, 130, 246, 0.7)',
      particles: '#3b82f6'
    }
  }), []);

  // Memoize sizes to prevent recreation
  const sizes = useMemo(() => ({
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-12 py-6 text-xl'
  }), []);

  const config = variants[variant];

  // FIXED: Only animate when hovered for better performance
  useEffect(() => {
    if (!isHovered) {
      setTime(0);
      return;
    }

    let animationId: number;
    let startTime = Date.now();
    let lastUpdate = 0;

    const animate = () => {
      const now = Date.now();
      // Throttle to 30fps for better performance
      if (now - lastUpdate >= 33) {
        const elapsed = (now - startTime) / 1000;
        setTime(elapsed);
        lastUpdate = now;
      }
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationId);
  }, [isHovered]);

  // Optimized and throttled mouse move handler
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    if (!buttonRef.current || disabled) return;

    const rect = buttonRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width * 0.5;
    const centerY = rect.top + rect.height * 0.5;

    const deltaX = e.clientX - centerX;
    const deltaY = e.clientY - centerY;

    // Optimized magnetic effect with reduced strength for better performance
    const magnetStrength = variant === 'magnetic' ? 0.2 : 0.06;
    const maxOffset = 8; // Limit maximum offset

    setMagneticOffset({
      x: Math.max(-maxOffset, Math.min(maxOffset, deltaX * magnetStrength)),
      y: Math.max(-maxOffset, Math.min(maxOffset, deltaY * magnetStrength))
    });
  }, [disabled, variant]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setMagneticOffset({ x: 0, y: 0 });
  }, []);

  const handleClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Optimized ripple effect with limited count
    if (ripples.length < 3) { // Limit concurrent ripples
      const rect = buttonRef.current!.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const newRipple = {
        id: Date.now(),
        x,
        y
      };

      setRipples(prev => [...prev.slice(-2), newRipple]); // Keep only last 2 ripples

      // Remove ripple after shorter animation
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 400); // Reduced from 600ms
    }

    // Sound effect (if enabled)
    if (soundEnabled) {
      // In a real app, you'd play an actual sound here
      console.log('🔊 Button sound effect');
    }

    onClick?.();
  }, [disabled, loading, ripples.length, soundEnabled, onClick]);

  const getTransform = () => {
    let transform = `translate(${magneticOffset.x}px, ${magneticOffset.y}px)`;
    
    if (isHovered) {
      transform += ' translateZ(10px) rotateX(5deg)';
      if (variant === 'quantum') {
        transform += ` rotateY(${Math.sin(time * 2) * 2}deg)`;
      }
    }
    
    if (isPressed) {
      transform += ' scale(0.98)';
    }

    return transform;
  };

  return (
    <button
      ref={buttonRef}
      className={`
        relative font-black uppercase tracking-wider rounded-xl overflow-hidden
        transition-all duration-300 ease-out transform-3d cursor-pointer
        disabled:opacity-50 disabled:cursor-not-allowed
        ${sizes[size]} ${className}
      `}
      style={{
        background: isHovered ? config.hover : config.base,
        transform: getTransform(),
        boxShadow: isHovered 
          ? `0 20px 40px ${config.glow}, 0 0 60px ${config.glow}` 
          : `0 10px 20px ${config.glow}`,
        transformStyle: 'preserve-3d',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onClick={handleClick}
      disabled={disabled || loading}
    >
      {/* Energy Field Background */}
      <div 
        className="absolute inset-0 opacity-30"
        style={{
          background: `radial-gradient(circle, ${config.particles} 0%, transparent 70%)`,
          transform: `scale(${1 + Math.sin(time * 3) * 0.1})`,
        }}
      />

      {/* Scanning Lines */}
      {isHovered && (
        <>
          <div
            className="absolute top-0 left-0 w-full h-0.5 bg-white"
            style={{
              animation: 'scan-horizontal 3s ease-in-out infinite',
            }}
          />
          <div
            className="absolute top-0 left-0 w-0.5 h-full bg-white"
            style={{
              animation: 'scan-vertical 3s ease-in-out infinite 1s',
            }}
          />
        </>
      )}

      {/* Ripple Effects */}
      {ripples.map(ripple => (
        <div
          key={ripple.id}
          className="absolute rounded-full border-2 border-white opacity-70 pointer-events-none"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: 0,
            height: 0,
            animation: 'ripple 0.6s ease-out forwards',
          }}
        />
      ))}

      {/* Optimized Particle Burst on Hover */}
      {isHovered && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(4)].map((_, i) => ( // Reduced from 8 to 4 particles
            <div
              key={i}
              className="absolute w-1 h-1 rounded-full animate-ping"
              style={{
                backgroundColor: config.particles,
                left: `${25 + Math.random() * 50}%`, // Reduced spread
                top: `${25 + Math.random() * 50}%`,
                animationDelay: `${Math.random() * 0.3}s`, // Reduced delay
                animationDuration: '0.8s', // Faster animation
              }}
            />
          ))}
        </div>
      )}

      {/* Button Content */}
      <div className="relative z-10 flex items-center justify-center space-x-2">
        {loading ? (
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        ) : (
          <>
            {icon && <span className="text-lg">{icon}</span>}
            <span>{children}</span>
          </>
        )}
      </div>

      {/* Holographic Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* 3D Border Effect */}
      <div 
        className="absolute inset-0 rounded-xl border border-white/30 opacity-0 transition-opacity duration-300"
        style={{
          opacity: isHovered ? 1 : 0,
          boxShadow: 'inset 0 0 20px rgba(255, 255, 255, 0.1)',
        }}
      />

      <style jsx>{`
        .transform-3d {
          transform-style: preserve-3d;
        }

        @keyframes scan-horizontal {
          0% {
            transform: translateY(-100%);
            opacity: 0;
          }
          10% {
            opacity: 0.6;
          }
          90% {
            opacity: 0.6;
          }
          100% {
            transform: translateY(100%);
            opacity: 0;
          }
        }

        @keyframes scan-vertical {
          0% {
            transform: translateX(-100%);
            opacity: 0;
          }
          10% {
            opacity: 0.6;
          }
          90% {
            opacity: 0.6;
          }
          100% {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        @keyframes ripple {
          to {
            width: 200px;
            height: 200px;
            margin-left: -100px;
            margin-top: -100px;
            opacity: 0;
          }
        }
      `}</style>
    </button>
  );
});

export default HyperButton;
