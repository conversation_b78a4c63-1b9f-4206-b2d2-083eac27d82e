import { useState, useEffect } from 'react';
import ImagePlaceholder from './ImagePlaceholder';
import HyperButton from './HyperButton';
import HyperBadge from './HyperBadge';
import { useAnimation } from '../utils/AnimationManager';

export default function About() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);

  // Use centralized animation system
  const [time, setTime] = useState(0);
  useAnimation('about-section', (currentTime) => {
    setTime(currentTime);
  }, 2); // Lower priority than hero

  const features = [
    {
      title: 'Curated Experiences',
      description: 'From sunset beach gatherings to underground warehouse parties, every event is carefully crafted to create authentic connections and unforgettable moments.',
      icon: '🎭',
      color: 'from-neon-pink to-neon-purple'
    },
    {
      title: 'Iconic Venues',
      description: 'We partner with San Diego&apos;s most unique spaces - from waterfront parks to rooftop terraces - creating the perfect backdrop for conscious celebration.',
      icon: '🏛️',
      color: 'from-neon-cyan to-neon-blue'
    },
    {
      title: 'Community First',
      description: 'More than events, we&apos;re building a movement. Join a community that values authenticity, mindfulness, and the transformative power of music and dance.',
      icon: '🤝',
      color: 'from-neon-green to-neon-yellow'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('#about-section');
    if (section) {
      observer.observe(section);
    }

    // Auto-rotate features
    const featureTimer = setInterval(() => {
      setActiveFeature(prev => (prev + 1) % features.length);
    }, 4000);

    return () => {
      observer.disconnect();
      clearInterval(featureTimer);
    };
  }, [features.length]);

  return (
    <section id="about-section" className="section-padding-large bg-black relative overflow-hidden">
      {/* Ultra-Dynamic Background */}
      <div className="absolute inset-0">
        {/* Morphing Energy Blobs */}
        <div
          className="absolute w-96 h-96 rounded-full opacity-15 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.5) 0%, transparent 70%)',
            left: `${20 + Math.sin(time * 0.3) * 15}%`,
            top: `${30 + Math.cos(time * 0.4) * 20}%`,
            transform: `scale(${1 + Math.sin(time * 0.5) * 0.3}) rotate(${time * 10}deg)`,
          }}
        />
        <div
          className="absolute w-80 h-80 rounded-full opacity-12 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(0, 255, 255, 0.5) 0%, transparent 70%)',
            right: `${15 + Math.cos(time * 0.6) * 12}%`,
            bottom: `${25 + Math.sin(time * 0.7) * 18}%`,
            transform: `scale(${1 + Math.cos(time * 0.4) * 0.2}) rotate(${-time * 8}deg)`,
          }}
        />

        {/* Data Stream Visualization */}
        <div className="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="data-stream" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
                <circle cx="50" cy="50" r="2" fill="rgba(255, 255, 255, 0.3)" />
                <line x1="50" y1="50" x2="100" y2="0" stroke="rgba(255, 255, 255, 0.1)" strokeWidth="1" />
                <line x1="50" y1="50" x2="0" y2="100" stroke="rgba(255, 255, 255, 0.1)" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#data-stream)" />
          </svg>
        </div>
      </div>

      <div className="content-container relative z-10">
        <div className="grid lg:grid-cols-2 gap-24 items-center">
          {/* Left Column - Enhanced Content */}
          <div className={`space-y-12 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="space-y-8">
              <div className="mb-8">
                <HyperBadge
                  type="new"
                  text="ABOUT US"
                  size="md"
                />
              </div>

              <h2 className="text-display-lg md:text-display-md font-black text-white leading-none tracking-tight">
                MANIFESTING DANCE FLOOR
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-purple to-neon-cyan animate-gradient-x">
                  EUPHORIA
                </span>
              </h2>

              <p className="text-event-subtitle md:text-display-xs font-semibold text-white/80 leading-tight">
                Since 2024, we&apos;ve been curating transformative experiences that bring together San Diego&apos;s most conscious community of music lovers, dancers, and free spirits.
              </p>
            </div>

            {/* Interactive Features Grid */}
            <div className="space-y-6">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`group relative p-6 glass rounded-2xl border transition-all duration-500 cursor-pointer ${
                    activeFeature === index
                      ? 'border-white/40 bg-white/10'
                      : 'border-white/10 hover:border-white/20'
                  }`}
                  onClick={() => setActiveFeature(index)}
                  onMouseEnter={() => setActiveFeature(index)}
                >
                  {/* Feature Icon */}
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-xl group-hover:scale-110 transition-transform duration-300`}>
                      {feature.icon}
                    </div>

                    <div className="flex-1">
                      <h3 className="text-xl font-black text-white uppercase tracking-wide mb-3 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-neon-pink group-hover:to-neon-cyan transition-all duration-500">
                        {feature.title}
                      </h3>
                      <p className="text-white/70 leading-relaxed group-hover:text-white/90 transition-colors duration-300">
                        {feature.description}
                      </p>
                    </div>
                  </div>

                  {/* Active Indicator */}
                  {activeFeature === index && (
                    <div className="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-neon-pink to-neon-cyan rounded-r-full animate-pulse-glow" />
                  )}

                  {/* Hover Particles */}
                  {activeFeature === index && (
                    <div className="absolute inset-0 pointer-events-none">
                      {[...Array(6)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-white rounded-full animate-ping"
                          style={{
                            left: `${20 + Math.random() * 60}%`,
                            top: `${20 + Math.random() * 60}%`,
                            animationDelay: `${Math.random() * 0.5}s`,
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Ultra-Modern CTA */}
            <div className="pt-8">
              <HyperButton
                variant="plasma"
                size="xl"
                onClick={() => console.log('Join the Collective clicked!')}
                icon="🚀"
                soundEnabled={true}
              >
                Join Collectiv
              </HyperButton>
            </div>
          </div>

          {/* Right Column - Futuristic Visual Showcase */}
          <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            {/* Main Holographic Display */}
            <div className="relative perspective-1000">
              <div className="relative transform-3d group">
                <ImagePlaceholder
                  aspectRatio="video"
                  className="w-full rounded-3xl shadow-2xl group-hover:scale-105 transition-transform duration-700"
                  alt="Conscious Collective experience"
                />

                {/* Holographic Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Floating Data Widgets */}
                <div className="absolute -top-6 -left-6 glass rounded-2xl p-4 border border-white/20 animate-float-slow">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-neon-pink to-neon-cyan rounded-xl flex items-center justify-center">
                      <span className="text-black font-black text-xs">CC</span>
                    </div>
                    <div className="space-y-1">
                      <div className="w-16 h-2 bg-white/30 rounded animate-pulse-glow"></div>
                      <div className="w-12 h-2 bg-white/20 rounded"></div>
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-6 -right-6 glass rounded-2xl p-4 border border-white/20 animate-float-slow" style={{ animationDelay: '1s' }}>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-neon-green rounded-full animate-pulse-glow"></div>
                      <div className="w-20 h-2 bg-white/30 rounded"></div>
                    </div>
                    <div className="w-16 h-2 bg-white/20 rounded"></div>
                    <div className="w-12 h-2 bg-white/20 rounded"></div>
                  </div>
                </div>

                <div className="absolute top-1/2 -right-8 transform -translate-y-1/2 glass rounded-2xl p-3 border border-white/20 animate-float-slow" style={{ animationDelay: '2s' }}>
                  <div className="w-8 h-8 bg-gradient-to-r from-neon-cyan to-neon-green rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full animate-ping"></div>
                  </div>
                </div>

                {/* Energy Streams */}
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(4)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-px h-8 bg-gradient-to-b from-neon-cyan to-transparent animate-pulse"
                      style={{
                        left: `${25 + i * 20}%`,
                        top: `${20 + i * 15}%`,
                        animationDelay: `${i * 0.3}s`,
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Interactive Grid */}
            <div className="mt-8 grid grid-cols-2 gap-6">
              <div className="relative group">
                <ImagePlaceholder
                  aspectRatio="square"
                  className="rounded-2xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                  alt="Community experience 1"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-neon-pink/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-4 left-4">
                  <HyperBadge type="trending" size="sm" />
                </div>
              </div>

              <div className="relative group">
                <ImagePlaceholder
                  aspectRatio="square"
                  className="rounded-2xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                  alt="Community experience 2"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-neon-cyan/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-4 left-4">
                  <HyperBadge type="vip" size="sm" />
                </div>
              </div>
            </div>

            {/* Floating Particles */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(8)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-white rounded-full animate-float-slow opacity-40"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 4}s`,
                    animationDuration: `${4 + Math.random() * 2}s`,
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
