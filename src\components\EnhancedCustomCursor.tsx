'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { simpleThrottle as throttle } from '../utils/throttle';
import AdvancedMouseEffects from './AdvancedMouseEffects';
import PerformanceMonitor from './PerformanceMonitor';

interface CursorTrail {
  id: number;
  x: number;
  y: number;
  opacity: number;
  size: number;
  timestamp: number;
}

interface MorphingCursor {
  baseSize: number;
  targetSize: number;
  morphSpeed: number;
  shape: 'circle' | 'square' | 'diamond' | 'star' | 'liquid';
  color: string;
  glow: boolean;
}

export default function EnhancedCustomCursor() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [velocity, setVelocity] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [cursorType, setCursorType] = useState('default');
  const [trails, setTrails] = useState<CursorTrail[]>([]);
  const [morphingCursor, setMorphingCursor] = useState<MorphingCursor>({
    baseSize: 8,
    targetSize: 8,
    morphSpeed: 0.1,
    shape: 'circle',
    color: 'rgba(0, 255, 255, 0.8)',
    glow: true
  });

  // Advanced effects state
  const [advancedEffectsEnabled, setAdvancedEffectsEnabled] = useState(false);
  const [performanceMode, setPerformanceMode] = useState<'high' | 'medium' | 'low'>('high');
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  // Performance refs
  const lastPosition = useRef({ x: 0, y: 0 });
  const lastTime = useRef(Date.now());
  const isVisible = useRef(true);
  const trailUpdateInterval = useRef<NodeJS.Timeout>();

  // Performance constants based on mode
  const performanceConfig = useMemo(() => ({
    high: {
      maxTrails: 15,
      trailUpdateInterval: 16,
      mouseThrottle: 4,
      morphingEnabled: true,
      glowEnabled: true
    },
    medium: {
      maxTrails: 8,
      trailUpdateInterval: 32,
      mouseThrottle: 8,
      morphingEnabled: true,
      glowEnabled: false
    },
    low: {
      maxTrails: 4,
      trailUpdateInterval: 50,
      mouseThrottle: 16,
      morphingEnabled: false,
      glowEnabled: false
    }
  }), []);

  const config = performanceConfig[performanceMode];

  // Optimized mouse tracking
  useEffect(() => {
    const updateMousePosition = throttle((e: MouseEvent) => {
      if (!isVisible.current) return;

      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime.current;
      
      const deltaX = e.clientX - lastPosition.current.x;
      const deltaY = e.clientY - lastPosition.current.y;
      const velocityX = deltaX / (deltaTime || 1);
      const velocityY = deltaY / (deltaTime || 1);

      setVelocity({ x: velocityX, y: velocityY });
      setMousePosition({ x: e.clientX, y: e.clientY });

      // Update morphing cursor based on velocity
      if (config.morphingEnabled) {
        const speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
        setMorphingCursor(prev => ({
          ...prev,
          targetSize: prev.baseSize + Math.min(speed * 0.5, 12),
          shape: speed > 2 ? 'diamond' : speed > 1 ? 'square' : 'circle'
        }));
      }

      // Create trail points
      if (trails.length < config.maxTrails) {
        const newTrail: CursorTrail = {
          id: currentTime + Math.random(),
          x: e.clientX,
          y: e.clientY,
          opacity: 0.8,
          size: 4 + Math.min(Math.sqrt(velocityX * velocityX + velocityY * velocityY), 8),
          timestamp: currentTime
        };
        
        setTrails(prev => [...prev.slice(-(config.maxTrails - 1)), newTrail]);
      }

      lastPosition.current = { x: e.clientX, y: e.clientY };
      lastTime.current = currentTime;
    }, config.mouseThrottle);

    const handleVisibilityChange = () => {
      isVisible.current = !document.hidden;
    };

    window.addEventListener('mousemove', updateMousePosition, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [config, trails.length]);

  // Trail decay system
  useEffect(() => {
    const updateTrails = () => {
      if (!isVisible.current) return;

      setTrails(prev => 
        prev.map(trail => ({
          ...trail,
          opacity: trail.opacity * 0.92,
          size: trail.size * 0.95
        })).filter(trail => trail.opacity > 0.1 && trail.size > 1)
      );
    };

    trailUpdateInterval.current = setInterval(updateTrails, config.trailUpdateInterval);

    return () => {
      if (trailUpdateInterval.current) {
        clearInterval(trailUpdateInterval.current);
      }
    };
  }, [config.trailUpdateInterval]);

  // Morphing animation
  useEffect(() => {
    if (!config.morphingEnabled) return;

    const animateMorphing = () => {
      setMorphingCursor(prev => ({
        ...prev,
        baseSize: prev.baseSize + (prev.targetSize - prev.baseSize) * prev.morphSpeed
      }));
    };

    const morphingInterval = setInterval(animateMorphing, 16);
    return () => clearInterval(morphingInterval);
  }, [config.morphingEnabled]);

  // Element detection for cursor type changes
  useEffect(() => {
    const handleMouseEnter = (e: Event) => {
      setIsHovering(true);
      const target = e.target as HTMLElement;

      if (target.tagName === 'BUTTON') {
        setCursorType('button');
        setMorphingCursor(prev => ({ ...prev, color: 'rgba(255, 255, 0, 0.8)', glow: true }));
      } else if (target.tagName === 'A') {
        setCursorType('link');
        setMorphingCursor(prev => ({ ...prev, color: 'rgba(0, 255, 255, 0.8)', glow: true }));
      } else if (target.classList.contains('glass')) {
        setCursorType('glass');
        setMorphingCursor(prev => ({ ...prev, color: 'rgba(255, 255, 255, 0.6)', glow: false }));
      } else {
        setCursorType('hover');
        setMorphingCursor(prev => ({ ...prev, color: 'rgba(255, 0, 128, 0.8)', glow: config.glowEnabled }));
      }
    };

    const handleMouseLeave = () => {
      setIsHovering(false);
      setCursorType('default');
      setMorphingCursor(prev => ({ ...prev, color: 'rgba(0, 255, 255, 0.8)', glow: config.glowEnabled }));
    };

    const interactiveElements = document.querySelectorAll('a, button, input, textarea, .cursor-glow, .glass');
    const elementsArray = Array.from(interactiveElements);

    elementsArray.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    return () => {
      elementsArray.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, [config.glowEnabled]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey) {
        switch (e.key) {
          case 'E':
            e.preventDefault();
            setAdvancedEffectsEnabled(prev => !prev);
            break;
          case 'P':
            e.preventDefault();
            setShowPerformanceMonitor(prev => !prev);
            break;
          case '1':
            e.preventDefault();
            setPerformanceMode('high');
            break;
          case '2':
            e.preventDefault();
            setPerformanceMode('medium');
            break;
          case '3':
            e.preventDefault();
            setPerformanceMode('low');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // Performance monitoring callback
  const handlePerformanceChange = useCallback((metrics: any) => {
    // Auto-adjust performance mode based on FPS
    if (metrics.fps < 30 && performanceMode !== 'low') {
      setPerformanceMode('low');
    } else if (metrics.fps < 45 && performanceMode === 'high') {
      setPerformanceMode('medium');
    } else if (metrics.fps > 55 && performanceMode !== 'high') {
      setPerformanceMode('high');
    }
  }, [performanceMode]);

  // Get cursor shape path
  const getCursorShape = (shape: string, size: number) => {
    const half = size / 2;
    switch (shape) {
      case 'square':
        return `M ${-half} ${-half} L ${half} ${-half} L ${half} ${half} L ${-half} ${half} Z`;
      case 'diamond':
        return `M 0 ${-half} L ${half} 0 L 0 ${half} L ${-half} 0 Z`;
      case 'star':
        const points = [];
        for (let i = 0; i < 5; i++) {
          const angle = (i * 144 - 90) * Math.PI / 180;
          const outerRadius = half;
          const innerRadius = half * 0.4;
          
          // Outer point
          points.push(`${Math.cos(angle) * outerRadius} ${Math.sin(angle) * outerRadius}`);
          
          // Inner point
          const innerAngle = ((i + 0.5) * 144 - 90) * Math.PI / 180;
          points.push(`${Math.cos(innerAngle) * innerRadius} ${Math.sin(innerAngle) * innerRadius}`);
        }
        return `M ${points[0]} L ${points.slice(1).join(' L ')} Z`;
      default:
        return ''; // Circle will use CSS border-radius
    }
  };

  return (
    <>
      {/* Hide default cursor */}
      <style jsx global>{`
        * {
          cursor: none !important;
        }
      `}</style>

      {/* Trail System */}
      {trails.map(trail => (
        <div
          key={trail.id}
          className="fixed pointer-events-none z-[9995]"
          style={{
            left: trail.x - trail.size / 2,
            top: trail.y - trail.size / 2,
            width: trail.size,
            height: trail.size,
            background: `radial-gradient(circle, ${morphingCursor.color} 0%, transparent 70%)`,
            borderRadius: '50%',
            opacity: trail.opacity,
            filter: config.glowEnabled ? `blur(0.5px)` : 'none',
          }}
        />
      ))}

      {/* Main Cursor */}
      <div
        className="fixed pointer-events-none z-[9996]"
        style={{
          left: mousePosition.x - morphingCursor.baseSize / 2,
          top: mousePosition.y - morphingCursor.baseSize / 2,
          width: morphingCursor.baseSize,
          height: morphingCursor.baseSize,
          transition: 'all 0.1s ease-out',
        }}
      >
        {morphingCursor.shape === 'circle' ? (
          <div
            style={{
              width: '100%',
              height: '100%',
              background: morphingCursor.color,
              borderRadius: '50%',
              boxShadow: morphingCursor.glow && config.glowEnabled 
                ? `0 0 ${morphingCursor.baseSize}px ${morphingCursor.color}` 
                : 'none',
              filter: config.glowEnabled ? 'blur(0.5px)' : 'none',
            }}
          />
        ) : (
          <svg width={morphingCursor.baseSize} height={morphingCursor.baseSize}>
            <defs>
              {morphingCursor.glow && config.glowEnabled && (
                <filter id="glow">
                  <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                  <feMerge> 
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              )}
            </defs>
            <path
              d={getCursorShape(morphingCursor.shape, morphingCursor.baseSize)}
              fill={morphingCursor.color}
              transform={`translate(${morphingCursor.baseSize / 2}, ${morphingCursor.baseSize / 2})`}
              filter={morphingCursor.glow && config.glowEnabled ? 'url(#glow)' : 'none'}
            />
          </svg>
        )}
      </div>

      {/* Advanced Effects */}
      {advancedEffectsEnabled && <AdvancedMouseEffects />}

      {/* Performance Monitor */}
      <PerformanceMonitor 
        enabled={showPerformanceMonitor}
        position="top-right"
        onPerformanceChange={handlePerformanceChange}
      />

      {/* Controls Indicator */}
      <div className="fixed bottom-4 left-4 z-[9999] text-white/40 text-xs font-mono">
        <div>Mode: {performanceMode.toUpperCase()}</div>
        <div>Effects: {advancedEffectsEnabled ? 'ON' : 'OFF'}</div>
        <div className="mt-1 text-white/30">
          <div>Ctrl+Shift+E: Toggle Effects</div>
          <div>Ctrl+Shift+P: Performance</div>
          <div>Ctrl+Shift+1/2/3: Quality</div>
        </div>
      </div>
    </>
  );
}
