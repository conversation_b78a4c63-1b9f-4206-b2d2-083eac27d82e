import { useState, useEffect, useRef } from 'react';
import VideoPlaceholder from './VideoPlaceholder';
import HyperButton from './HyperButton';
import { useAnimation } from '../utils/AnimationManager';
import { throttle } from '../utils/throttle';

export default function PartyWithUs() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const sectionRef = useRef<HTMLElement>(null);

  // Use centralized animation system
  const [time, setTime] = useState(0);
  useAnimation('party-with-us', (currentTime) => {
    setTime(currentTime);
  }, 1); // Medium priority

  // Enhanced animations and interactions
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Optimized mouse move handler with throttling and movement threshold
  const handleMouseMove = throttle((e: React.MouseEvent) => {
    if (!sectionRef.current) return;

    const rect = sectionRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;
    const newPosition = { x: x - 0.5, y: y - 0.5 };

    // Only update if movement is significant (reduces repaints)
    const threshold = 0.02; // 2% of container size
    if (Math.abs(newPosition.x - mousePosition.x) > threshold ||
        Math.abs(newPosition.y - mousePosition.y) > threshold) {
      setMousePosition(newPosition);
    }
  }, 100); // 10fps throttling for better performance

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!formData.name || !formData.email || !formData.message) return;

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitted(true);
      setIsLoading(false);
      setFormData({ name: '', email: '', message: '' });
    }, 1000);
  };

  return (
    <section
      ref={sectionRef}
      className="relative section-padding-large overflow-hidden"
      onMouseMove={handleMouseMove}
      style={{
        background: `
          radial-gradient(circle at ${50 + mousePosition.x * 20}% ${50 + mousePosition.y * 20}%,
            rgba(255, 0, 128, 0.15) 0%,
            rgba(0, 255, 255, 0.1) 40%,
            transparent 70%),
          radial-gradient(circle at ${80 - mousePosition.x * 15}% ${30 - mousePosition.y * 15}%,
            rgba(255, 255, 0, 0.1) 0%,
            rgba(255, 0, 255, 0.08) 50%,
            transparent 80%),
          linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)
        `
      }}
    >
      {/* Dynamic Background Layers */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Animated Gradient Mesh */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              conic-gradient(from ${time * 20}deg at 30% 70%,
                rgba(255, 0, 128, 0.4) 0deg,
                rgba(0, 255, 255, 0.3) 120deg,
                rgba(255, 255, 0, 0.3) 240deg,
                rgba(255, 0, 128, 0.4) 360deg)
            `,
            transform: `scale(${1 + Math.sin(time) * 0.1}) rotate(${time * 5}deg)`,
            filter: 'blur(60px)',
          }}
        />

        {/* Floating Particles */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-neon-cyan rounded-full animate-pulse-glow"
            style={{
              left: `${10 + (i * 7) % 80}%`,
              top: `${20 + (i * 11) % 60}%`,
              transform: `
                translateX(${Math.sin(time + i) * 20 + mousePosition.x * 30}px)
                translateY(${Math.cos(time + i * 0.7) * 15 + mousePosition.y * 20}px)
                scale(${0.5 + Math.sin(time + i) * 0.3})
              `,
              animationDelay: `${i * 0.2}s`,
              opacity: 0.6 + Math.sin(time + i) * 0.4,
            }}
          />
        ))}
      </div>

      <div className="content-container relative z-10">
        {/* Ultra-Modern Section Header */}
        <div className={`text-center mb-20 transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="relative inline-block">
            <h2 className="text-6xl md:text-8xl font-black text-3d-neon mb-6 tracking-tighter">
              PARTY WITH US
            </h2>
            {/* Holographic overlay on title */}
            <div className="absolute inset-0 bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-yellow opacity-30 bg-clip-text text-transparent animate-gradient-x" />
          </div>
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Ready to transcend the ordinary? Connect with the collective and let&apos;s architect
            <span className="text-neon-cyan font-semibold"> extraordinary experiences </span>
            together.
          </p>
        </div>

        {/* Ultra-Modern Grid Layout */}
        <div className="grid lg:grid-cols-2 gap-20 items-start">
          {/* Left Column - Futuristic Contact Form */}
          <div className={`transition-all duration-1000 delay-300 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            {!isSubmitted ? (
              <div className="glass-ultra neuro-dynamic p-8 rounded-3xl relative overflow-hidden">
                {/* Form Background Effects */}
                <div className="absolute inset-0 bg-gradient-mesh opacity-20 animate-gradient-mesh-flow" />
                <div
                  className="absolute inset-0 opacity-10"
                  style={{
                    background: `radial-gradient(circle at ${50 + mousePosition.x * 100}px ${50 + mousePosition.y * 100}px, rgba(255, 0, 128, 0.3) 0%, transparent 50%)`
                  }}
                />

                <form onSubmit={handleSubmit} className="space-y-8 relative z-10">
                  <div className="space-y-6">
                    <div className="group">
                      <label htmlFor="name" className="block text-sm font-bold text-neon-cyan mb-3 uppercase tracking-wider">
                        Identity Vector *
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          placeholder="Your designation..."
                          className="w-full px-6 py-4 bg-black/50 border-2 border-white/20 rounded-2xl text-white placeholder:text-white/40 focus:outline-none focus:border-neon-cyan focus:bg-black/70 transition-all duration-300 backdrop-blur-sm group-hover:border-white/40"
                          required
                        />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-neon-pink/20 to-neon-cyan/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                      </div>
                    </div>

                    <div className="group">
                      <label htmlFor="email" className="block text-sm font-bold text-neon-cyan mb-3 uppercase tracking-wider">
                        Neural Link *
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          placeholder="<EMAIL>"
                          className="w-full px-6 py-4 bg-black/50 border-2 border-white/20 rounded-2xl text-white placeholder:text-white/40 focus:outline-none focus:border-neon-cyan focus:bg-black/70 transition-all duration-300 backdrop-blur-sm group-hover:border-white/40"
                          required
                        />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-neon-pink/20 to-neon-cyan/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                      </div>
                    </div>

                    <div className="group">
                      <label htmlFor="message" className="block text-sm font-bold text-neon-cyan mb-3 uppercase tracking-wider">
                        Transmission *
                      </label>
                      <div className="relative">
                        <textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          placeholder="Describe your vision, energy, and the experiences that call to your soul..."
                          rows={6}
                          className="w-full px-6 py-4 bg-black/50 border-2 border-white/20 rounded-2xl text-white placeholder:text-white/40 focus:outline-none focus:border-neon-cyan focus:bg-black/70 transition-all duration-300 backdrop-blur-sm resize-none group-hover:border-white/40"
                          required
                        />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-neon-pink/20 to-neon-cyan/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                      </div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <HyperButton
                      variant="quantum"
                      size="xl"
                      onClick={handleSubmit}
                      disabled={isLoading}
                      loading={isLoading}
                      icon="🚀"
                      className="w-full"
                    >
                      {isLoading ? 'TRANSMITTING...' : 'INITIATE CONNECTION'}
                    </HyperButton>
                  </div>
                </form>
              </div>
            ) : (
              <div className="glass-ultra neuro-dynamic p-8 rounded-3xl text-center relative overflow-hidden">
                {/* Success Animation Background */}
                <div className="absolute inset-0 bg-gradient-to-r from-neon-cyan/20 via-neon-pink/20 to-neon-yellow/20 animate-gradient-x" />

                <div className="relative z-10">
                  <div className="w-24 h-24 bg-gradient-to-r from-neon-cyan to-neon-pink rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse-glow">
                    <svg className="w-12 h-12 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-black text-neon-cyan mb-4 uppercase tracking-wider">
                    TRANSMISSION RECEIVED
                  </h3>
                  <p className="text-white/80 text-lg leading-relaxed mb-6">
                    Your signal has been captured by our neural network.
                    <br />
                    <span className="text-neon-pink font-semibold">Quantum response incoming within 24 hours.</span>
                  </p>
                  <HyperButton
                    variant="ghost"
                    size="md"
                    onClick={() => setIsSubmitted(false)}
                    icon="🔄"
                  >
                    SEND ANOTHER TRANSMISSION
                  </HyperButton>
                </div>
              </div>
            )}

            {/* Futuristic Contact Info */}
            <div className="mt-12 pt-8 border-t border-neon-cyan/30">
              <div className="space-y-6">
                <div className="flex items-center space-x-4 group">
                  <div className="w-12 h-12 bg-gradient-to-r from-neon-pink to-neon-cyan rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-neon-cyan font-bold uppercase tracking-wider">Neural Link</p>
                    <p className="text-white font-medium"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 group">
                  <div className="w-12 h-12 bg-gradient-to-r from-neon-yellow to-neon-orange rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-neon-cyan font-bold uppercase tracking-wider">Dimension</p>
                    <p className="text-white font-medium">Southern California</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Immersive Video Experience */}
          <div className={`relative transition-all duration-1000 delay-500 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="relative group">
              {/* Video Container with Advanced Effects */}
              <div className="glass-ultra neuro-dynamic rounded-3xl overflow-hidden relative">
                <VideoPlaceholder
                  className="w-full h-96 lg:h-[600px]"
                  showPlayButton={false}
                  overlay={true}
                  aspectRatio="video"
                  alt="Conscious Collective immersive experience"
                >
                  <div className="text-center text-white relative z-10">
                    <div className="mb-6">
                      <div className="w-32 h-32 bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-yellow rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse-glow group-hover:scale-110 transition-transform duration-500">
                        <svg className="w-16 h-16 text-black" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    </div>
                    <h3 className="text-3xl font-black text-3d-neon mb-4 uppercase tracking-wider">
                      COLLECTIVE ENERGY
                    </h3>
                    <p className="text-lg text-white/80 mb-2">Experience the frequency</p>
                    <p className="text-sm text-neon-cyan font-semibold">Neural synchronization in progress...</p>
                  </div>
                </VideoPlaceholder>

                {/* Holographic Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-neon-pink/20 via-transparent to-neon-cyan/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              </div>

              {/* Advanced Floating Elements */}
              <div className="absolute -top-6 -right-6 w-12 h-12 bg-gradient-to-r from-neon-pink to-neon-cyan rounded-2xl animate-pulse-glow rotate-45"
                   style={{ transform: `rotate(${45 + time * 10}deg) scale(${1 + Math.sin(time) * 0.1})` }} />
              <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-gradient-to-r from-neon-yellow to-neon-orange rounded-xl animate-pulse-glow"
                   style={{
                     transform: `rotate(${-45 + time * -15}deg) scale(${1 + Math.cos(time) * 0.1})`,
                     animationDelay: '1s'
                   }} />
              <div className="absolute top-1/2 -right-4 w-6 h-6 bg-neon-cyan rounded-full animate-pulse-glow"
                   style={{
                     transform: `translateY(${Math.sin(time * 2) * 10}px) scale(${0.8 + Math.sin(time) * 0.2})`,
                     animationDelay: '2s'
                   }} />

              {/* Orbiting Particles */}
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-3 h-3 bg-white rounded-full opacity-60"
                  style={{
                    top: '50%',
                    left: '50%',
                    transform: `
                      translateX(-50%) translateY(-50%)
                      rotate(${time * 30 + i * 60}deg)
                      translateX(${120 + Math.sin(time + i) * 20}px)
                      rotate(${-time * 30 - i * 60}deg)
                      scale(${0.5 + Math.sin(time + i) * 0.3})
                    `,
                    filter: `hue-rotate(${i * 60}deg)`,
                  }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Bottom CTA Section */}
        <div className={`text-center mt-24 transition-all duration-1000 delay-700 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="glass-ultra rounded-3xl p-8 max-w-4xl mx-auto">
            <h3 className="text-4xl font-black text-3d-neon mb-6 uppercase tracking-wider">
              JOIN THE FREQUENCY
            </h3>
            <p className="text-xl text-white/80 mb-8 leading-relaxed">
              Get first access to tickets, exclusive events, and become part of San Diego&apos;s most conscious community
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <HyperButton
                variant="holographic"
                size="xl"
                onClick={() => console.log('Get Tickets clicked!')}
                icon="🎫"
                soundEnabled={true}
              >
                GET TICKETS
              </HyperButton>
              <HyperButton
                variant="plasma"
                size="xl"
                onClick={() => console.log('Join Newsletter clicked!')}
                icon="⭐"
                soundEnabled={true}
              >
                JOIN NEWSLETTER
              </HyperButton>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
