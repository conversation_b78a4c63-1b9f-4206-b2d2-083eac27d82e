'use client';

import { useState, useEffect, useRef } from 'react';
import EventCard from './EventCard';
import HyperBadge from './HyperBadge';
import HyperButton from './HyperButton';
import { useAnimation } from '../utils/AnimationManager';

// Mock events data - expanded from the existing data
const allEvents = [
  {
    id: '1',
    title: 'Sunset Beach Vibes',
    date: '2025-07-15',
    location: 'Manhattan Beach, CA',
    category: 'Beach',
    featured: true,
  },
  {
    id: '2',
    title: 'Conscious Pool Party',
    date: '2025-07-22',
    location: 'Venice, CA',
    category: 'Pool',
    featured: true,
  },
  {
    id: '3',
    title: 'Full Moon Gathering',
    date: '2025-07-29',
    location: 'Malibu, CA',
    category: 'Spiritual',
    featured: true,
  },
  {
    id: '4',
    title: 'Rooftop Connections',
    date: '2025-08-05',
    location: 'West Hollywood, CA',
    category: 'Rooftop',
    featured: false,
  },
  {
    id: '5',
    title: 'Desert Retreat',
    date: '2025-08-12',
    location: 'Joshua Tree, CA',
    category: 'Retreat',
    featured: false,
  },
  {
    id: '6',
    title: 'Underground Warehouse',
    date: '2025-08-19',
    location: 'Downtown LA, CA',
    category: 'Warehouse',
    featured: false,
  },
  {
    id: '7',
    title: 'Beachside Bonfire',
    date: '2025-08-26',
    location: 'Hermosa Beach, CA',
    category: 'Beach',
    featured: false,
  },
  {
    id: '8',
    title: 'Conscious Brunch',
    date: '2025-09-02',
    location: 'Santa Monica, CA',
    category: 'Brunch',
    featured: false,
  },
];

const categories = ['All', 'Beach', 'Pool', 'Spiritual', 'Rooftop', 'Retreat', 'Warehouse', 'Brunch'];

export default function EventsContent() {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [time, setTime] = useState(0);
  const heroRef = useRef<HTMLDivElement>(null);

  // Animation system
  useAnimation('events-page', (currentTime) => {
    setTime(currentTime);
  }, 1);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (heroRef.current) {
      observer.observe(heroRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Filter events based on selected category
  const filteredEvents = selectedCategory === 'All' 
    ? allEvents 
    : allEvents.filter(event => event.category === selectedCategory);

  return (
    <>
      {/* Hero Section */}
      <section 
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden bg-black"
      >
        {/* Ultra-Modern Background System */}
        <div className="absolute inset-0">
          {/* Animated Gradient Base */}
          <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
            <div className="absolute inset-0 bg-gradient-to-r from-neon-pink/10 via-neon-cyan/10 to-neon-green/10 animate-gradient-x" />
          </div>

          {/* Dynamic Grid Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div
              className="w-full h-full"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(255, 0, 128, 0.3) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(0, 255, 255, 0.3) 1px, transparent 1px)
                `,
                backgroundSize: '60px 60px',
                transform: `translate(${Math.sin(time * 0.2) * 5}px, ${Math.cos(time * 0.15) * 5}px)`,
              }}
            />
          </div>

          {/* Floating Orbs */}
          <div className="absolute inset-0">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="absolute w-96 h-96 rounded-full blur-3xl opacity-20"
                style={{
                  background: `radial-gradient(circle, ${
                    i === 0 ? 'rgba(255, 0, 128, 0.4)' :
                    i === 1 ? 'rgba(0, 255, 255, 0.4)' :
                    'rgba(255, 255, 0, 0.4)'
                  } 0%, transparent 70%)`,
                  left: `${20 + i * 30}%`,
                  top: `${30 + i * 20}%`,
                  transform: `translate(${Math.sin(time * 0.3 + i) * 20}px, ${Math.cos(time * 0.2 + i) * 15}px)`,
                }}
              />
            ))}
          </div>
        </div>

        {/* Hero Content */}
        <div className="relative z-20 text-center px-6 max-w-6xl mx-auto">
          <div className={`transition-all duration-1000 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
            <div className="mb-8">
              <HyperBadge
                type="live"
                text="EVENTS & EXPERIENCES"
                size="lg"
              />
            </div>

            <h1 className="text-6xl md:text-8xl font-black text-white mb-8 leading-none tracking-tight">
              DISCOVER
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-neon-purple to-neon-cyan">
                EXPERIENCES
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed">
              Join our conscious community for transformative events, meaningful connections, 
              and unforgettable experiences across Southern California.
            </p>
          </div>
        </div>
      </section>

      {/* Events Grid Section */}
      <section className="relative py-32 bg-black">
        <div className="content-container">
          {/* Category Filter */}
          <div className="mb-16 text-center">
            <div className="inline-flex flex-wrap gap-4 p-2 glass rounded-2xl border border-white/10">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`
                    px-6 py-3 rounded-xl font-bold text-sm uppercase tracking-wide transition-all duration-300
                    ${selectedCategory === category
                      ? 'bg-gradient-to-r from-neon-pink to-neon-cyan text-black'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                    }
                  `}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Events Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {filteredEvents.map((event, index) => (
              <div
                key={event.id}
                className="transition-all duration-500"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <EventCard {...event} />
              </div>
            ))}
          </div>

          {/* Load More Button */}
          <div className="text-center">
            <HyperButton
              variant="holographic"
              size="lg"
              onClick={() => console.log('Load more events')}
              icon="🔄"
            >
              Load More Events
            </HyperButton>
          </div>
        </div>
      </section>
    </>
  );
}
