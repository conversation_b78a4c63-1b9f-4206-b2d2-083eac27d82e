import Link from 'next/link';
import VideoPlaceholder from './VideoPlaceholder';
import HyperButton from './HyperButton';
import HyperBadge from './HyperBadge';
import HeroVideoLogo from './HeroVideoLogo';
import { useEffect, useState, useCallback, useRef } from 'react';
import { throttle } from '../utils/throttle';

import { performanceManager } from '../utils/PerformanceManager';

export default function Hero() {
  const [scrollY, setScrollY] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [interactionsEnabled, setInteractionsEnabled] = useState(false); // New: defer interactions

  const isVisibleRef = useRef(true);
  const heroRef = useRef<HTMLDivElement>(null);
  const lastScrollY = useRef(0);
  const lastMousePosition = useRef({ x: 0, y: 0 });
  const hasAnimationStarted = useRef(false); // Prevent animation restart



  // Optimized scroll handler with reduced frequency and early exit
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleScroll = useCallback(throttle(() => {
    if (!isVisibleRef.current) return;

    const currentScrollY = window.scrollY;
    // Only update if scroll difference is significant
    if (Math.abs(currentScrollY - lastScrollY.current) > 2) {
      setScrollY(currentScrollY);
      lastScrollY.current = currentScrollY;
    }
  }, 32), []); // Reduced to 30fps for better performance

  // Heavily optimized mouse move handler with aggressive throttling
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleMouseMove = useCallback(throttle((e: MouseEvent) => {
    // OPTIMIZATION: Only handle mouse moves after interactions are enabled (3-5+ seconds)
    if (!isVisibleRef.current || !heroRef.current || !interactionsEnabled) return;

    const rect = heroRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    // Increased threshold for significant movement (reduces repaints)
    if (Math.abs(x - lastMousePosition.current.x) > 3 || Math.abs(y - lastMousePosition.current.y) > 3) {
      setMousePosition({ x, y });
      lastMousePosition.current = { x, y };
    }
  }, 100), [interactionsEnabled]); // Further reduced to 10fps for optimal performance

  useEffect(() => {
    // Visibility API for performance optimization
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
    };

    // Add event listeners with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Wait for performance optimization signal before starting animations
    const handlePerformanceReady = () => {
      // Prevent duplicate animation starts
      if (hasAnimationStarted.current) {
        console.log('Hero: Animation already started, ignoring performance-ready event');
        return;
      }

      // Additional safety check
      if (!heroRef.current) {
        console.warn('Hero: heroRef not available, delaying animation start');
        setTimeout(handlePerformanceReady, 100);
        return;
      }

      hasAnimationStarted.current = true;
      console.log('Hero: Starting animations via performance-ready event');
      setIsVisible(true);
    };

    // Use the new performance manager instead of direct event listeners
    const cleanup = performanceManager.onPerformanceReady(handlePerformanceReady);

    // Fallback timer in case performance manager doesn't fire
    const fallbackTimer = setTimeout(() => {
      if (!hasAnimationStarted.current) {
        console.log('Hero: Fallback timer triggered, starting animations');
        handlePerformanceReady();
      }
    }, 1000); // 1 second fallback

    // OPTIMIZATION: Enable interactions after 5 seconds for better initial FPS
    const interactionsTimer = setTimeout(() => {
      console.log('Hero: Enabling interactions after 5 seconds');
      setInteractionsEnabled(true);
    }, 5000); // 5 second delay for interactions

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      cleanup(); // Clean up performance manager listener
      clearTimeout(fallbackTimer);
      clearTimeout(interactionsTimer);
    };
  }, [handleScroll]);

  const scrollToEvents = () => {
    const eventsSection = document.getElementById('events');
    if (eventsSection) {
      eventsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section
      ref={heroRef}
      className="relative h-screen w-full overflow-hidden bg-black flex items-center justify-center"
      onMouseMove={interactionsEnabled ? handleMouseMove : undefined}
    >
      {/* Ultra-Modern Background System */}
      <div className="absolute inset-0">
        {/* Simplified Grid Background */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 0, 128, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '60px 60px',
              transform: 'translate(0px, 0px)',
            }}
          />
        </div>

        {/* Video Background with Advanced Parallax */}
        <div
          className="absolute inset-0 scale-110"
          style={{
            transform: `translateY(${scrollY * 0.3}px) scale(${1 + scrollY * 0.0001})`,
            filter: 'blur(1px) saturate(120%)',
          }}
        >
          <VideoPlaceholder
            className="w-full h-full object-cover opacity-30"
            showPlayButton={false}
            overlay={false}
            aspectRatio="video"
            alt="Conscious Collective hero video"
          />
        </div>

        {/* Multi-Layer Gradient System */}
        <div className="absolute inset-0 bg-gradient-to-br from-neon-pink/20 via-black/60 to-neon-cyan/20 animate-gradient-x" />
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-neon-purple/15 to-transparent animate-gradient-y" />

        {/* Simplified Chromatic Aberration Layer */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            background: `
              radial-gradient(circle at 50% 50%,
                rgba(255, 0, 128, 0.2) 0%,
                rgba(0, 255, 255, 0.1) 50%,
                transparent 100%
              )
            `,
            filter: 'blur(1px)',
            mixBlendMode: 'screen',
          }}
        />

        {/* Floating Orbs with 3D Effect */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-neon-pink/40 to-neon-purple/40 rounded-full blur-3xl animate-float-slow transform-3d rotate-x-12" />
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-neon-cyan/35 to-neon-green/35 rounded-full blur-3xl animate-float-slow transform-3d rotate-y-12" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-neon-yellow/30 to-neon-orange/30 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: '4s' }} />

        {/* Dynamic Grid Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full animate-pulse-glow"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 0, 128, 0.4) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.4) 1px, transparent 1px)
              `,
              backgroundSize: '80px 80px',
              transform: `translateY(${scrollY * 0.2}px)`
            }}
          />
        </div>



        {/* Quantum Field Effect */}
        <div className="absolute inset-0 opacity-10">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="quantum-field" x="0" y="0" width="150" height="150" patternUnits="userSpaceOnUse">
                <circle
                  cx="75"
                  cy="75"
                  r="4"
                  fill="rgba(255, 0, 128, 0.6)"
                />
                <line
                  x1="75"
                  y1="75"
                  x2="100"
                  y2="75"
                  stroke="rgba(0, 255, 255, 0.4)"
                  strokeWidth="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#quantum-field)" />
          </svg>
        </div>


      </div>
      {/* Ultra-Modern Hero Content */}
      <div className="relative z-20 flex flex-col items-center justify-center h-full text-center px-6">


        {/* Glitch Logo with Ultra-Modern Effects */}
        <div className={`mb-20 w-full max-w-4xl md:max-w-6xl lg:max-w-7xl xl:max-w-8xl transition-all duration-1500 delay-300 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="relative group">
            {/* Simplified Glitch Logo Container */}
            <div className="relative">
              {/* Single Main Logo with Subtle Glitch Effect */}
              <div className="relative">
                {/* Main Logo */}
                <div className="relative z-10">
                  <HeroVideoLogo className="w-full" />
                </div>


              </div>
            </div>

            {/* Holographic Frame */}
            <div className="absolute inset-0 rounded-3xl p-0.5 opacity-60 group-hover:opacity-100 transition-opacity duration-500"
              style={{
                background: 'conic-gradient(from 0deg, rgba(255, 0, 128, 0.8), rgba(0, 255, 255, 0.8), rgba(255, 255, 0, 0.8), rgba(255, 0, 128, 0.8))',
              }}
            >
              <div className="w-full h-full bg-black rounded-3xl" />
            </div>


          </div>
        </div>

        {/* Next-Gen Call-to-Action System */}
        <div className={`flex flex-col sm:flex-row gap-8 mb-20 transition-all duration-1500 delay-1200 ${isVisible ? 'animate-slide-up-fade' : 'opacity-0'}`}>
          <div className="relative group">
            <Link href="/events">
              <HyperButton
                variant="primary"
                size="xl"
                icon="🚀"
                soundEnabled={true}
              >
                View Events
              </HyperButton>
            </Link>

          </div>

          <div className="relative group">
            <HyperButton
              variant="holographic"
              size="xl"
              onClick={() => console.log('Join Collective clicked!')}
              icon="⭐"
              soundEnabled={true}
            >
              Join Collectiv
            </HyperButton>

          </div>
        </div>
      </div>

      {/* Advanced Mouse Follower System - Moved outside content container */}
      <div
        className="absolute w-96 h-96 rounded-full opacity-15 blur-3xl pointer-events-none z-10"
        style={{
          background: `
            radial-gradient(circle,
              rgba(255, 0, 128, 0.6) 0%,
              rgba(0, 255, 255, 0.4) 40%,
              transparent 70%
            )
          `,
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
          transform: 'translate(-50%, -50%)',
          transition: 'all 0.2s ease-out',
        }}
      />

      {/* Secondary Mouse Trail - Moved outside content container */}
      <div
        className="absolute w-64 h-64 rounded-full opacity-10 blur-2xl pointer-events-none z-10"
        style={{
          background: `
            conic-gradient(from 0deg,
              rgba(255, 0, 128, 0.8) 0deg,
              rgba(0, 255, 255, 0.6) 120deg,
              rgba(255, 255, 0, 0.4) 240deg,
              rgba(255, 0, 128, 0.8) 360deg
            )
          `,
          left: mousePosition.x - 128,
          top: mousePosition.y - 128,
          transform: 'translate(-50%, -50%)',
          transition: 'all 0.4s ease-out',
        }}
      />

      {/* Ambient Light Effects */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-1/3 bg-gradient-to-b from-neon-pink/10 to-transparent" />
        <div className="absolute bottom-0 left-0 w-full h-1/3 bg-gradient-to-t from-neon-cyan/10 to-transparent" />
        <div className="absolute left-0 top-0 w-1/3 h-full bg-gradient-to-r from-neon-purple/5 to-transparent" />
        <div className="absolute right-0 top-0 w-1/3 h-full bg-gradient-to-l from-neon-yellow/5 to-transparent" />
      </div>
    </section>
  );
}
