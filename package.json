{"name": "consciouscollective", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "perf:lighthouse": "lighthouse http://localhost:3000 --output=json --output-path=./lighthouse-report.json --chrome-flags='--headless'", "perf:bundle": "npm run build:analyze"}, "dependencies": {"@vercel/analytics": "^1.5.0", "next": "^13.5.11", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.34.2"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"@types/node": "^18.16.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.39.0", "eslint-config-next": "^13.4.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "^5.0.4", "webpack-bundle-analyzer": "^4.10.1"}}