/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'media',
  theme: {
    extend: {
      colors: {
        // Primary backgrounds - Event/Retro inspired
        light: '#FFFFFF',
        dark: '#0A0A0A',
        'surface': '#FAFAFA',
        'surface-elevated': '#FFFFFF',

        // Text colors - Bold hierarchy
        'text-primary': '#0A0A0A',
        'text-secondary': '#404040',
        'text-tertiary': '#737373',
        'text-inverse': '#FFFFFF',

        // Brand colors - Neon/Retro inspired
        'brand-primary': '#FF0080', // Hot pink/magenta
        'brand-secondary': '#00FFFF', // Cyan
        'brand-tertiary': '#FFFF00', // Electric yellow
        'gradient-start': '#FF0080',
        'gradient-end': '#00FFFF',

        // Neon accent colors
        'neon-pink': '#FF0080',
        'neon-cyan': '#00FFFF',
        'neon-yellow': '#FFFF00',
        'neon-green': '#00FF41',
        'neon-purple': '#8A2BE2',
        'neon-orange': '#FF4500',

        // Event-specific colors
        'event-primary': '#FF0080',
        'event-secondary': '#00FFFF',
        'event-accent': '#FFFF00',

        // Status colors
        'success': '#00FF41',
        'warning': '#FFFF00',
        'error': '#FF0040',
        'info': '#00FFFF',

        // Extended grays - High contrast
        gray: {
          25: '#FCFCFD',
          50: '#F8F9FA',
          100: '#F1F3F4',
          200: '#E8EAED',
          300: '#DADCE0',
          400: '#9AA0A6',
          500: '#5F6368',
          600: '#3C4043',
          700: '#202124',
          800: '#171717',
          900: '#0F0F0F',
          950: '#0A0A0A',
        },

        // Border colors
        'border-primary': '#E8EAED',
        'border-secondary': '#F1F3F4',
        'border-tertiary': '#DADCE0',

        // Legacy support
        gold: '#FFFF00',
      },
      fontFamily: {
        sans: ['Inter', 'Neue Haas Grotesk', 'system-ui', 'sans-serif'],
        display: ['Inter', 'Neue Haas Grotesk', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        // Bold, blocky typography - Event/Retro inspired
        'display-2xl': ['96px', { lineHeight: '96px', fontWeight: '900', letterSpacing: '-0.04em' }],
        'display-xl': ['80px', { lineHeight: '80px', fontWeight: '900', letterSpacing: '-0.04em' }],
        'display-lg': ['64px', { lineHeight: '64px', fontWeight: '800', letterSpacing: '-0.03em' }],
        'display-md': ['48px', { lineHeight: '48px', fontWeight: '800', letterSpacing: '-0.03em' }],
        'display-sm': ['36px', { lineHeight: '40px', fontWeight: '700', letterSpacing: '-0.02em' }],
        'display-xs': ['28px', { lineHeight: '32px', fontWeight: '700', letterSpacing: '-0.02em' }],

        // Text sizes - Bold and impactful
        'text-xl': ['24px', { lineHeight: '32px', fontWeight: '600' }],
        'text-lg': ['20px', { lineHeight: '28px', fontWeight: '500' }],
        'text-md': ['18px', { lineHeight: '26px', fontWeight: '500' }],
        'text-sm': ['16px', { lineHeight: '24px', fontWeight: '400' }],
        'text-xs': ['14px', { lineHeight: '20px', fontWeight: '400' }],

        // Event-specific sizes
        'event-hero': ['120px', { lineHeight: '100px', fontWeight: '900', letterSpacing: '-0.05em' }],
        'event-title': ['72px', { lineHeight: '72px', fontWeight: '800', letterSpacing: '-0.04em' }],
        'event-subtitle': ['32px', { lineHeight: '40px', fontWeight: '600', letterSpacing: '-0.02em' }],

        // Legacy support - Updated to be bolder
        'h1': ['80px', { lineHeight: '80px', fontWeight: '900', letterSpacing: '-0.04em' }],
        'h2': ['48px', { lineHeight: '48px', fontWeight: '800', letterSpacing: '-0.03em' }],
        'h3': ['28px', { lineHeight: '32px', fontWeight: '700', letterSpacing: '-0.02em' }],
        'body': ['18px', { lineHeight: '26px', fontWeight: '500' }],
        'caption': ['14px', { lineHeight: '20px', fontWeight: '600', letterSpacing: '0.1em', textTransform: 'uppercase' }],

        // Responsive versions for mobile - Still bold
        'h1-mobile': ['56px', { lineHeight: '56px', fontWeight: '900', letterSpacing: '-0.04em' }],
        'h2-mobile': ['36px', { lineHeight: '40px', fontWeight: '800', letterSpacing: '-0.03em' }],
        'h3-mobile': ['24px', { lineHeight: '28px', fontWeight: '700', letterSpacing: '-0.02em' }],
        'body-mobile': ['16px', { lineHeight: '24px', fontWeight: '500' }],
      },
      spacing: {
        // 8pt base grid system
        '2': '8px',   // 1 unit
        '4': '16px',  // 2 units
        '6': '24px',  // 3 units
        '8': '32px',  // 4 units
        '12': '48px', // 6 units
        '16': '64px', // 8 units
        '20': '80px', // 10 units
        '24': '96px', // 12 units - section padding desktop
        '32': '128px',
        '40': '160px',
        '48': '192px',
        '56': '224px',
        '64': '256px',
        
        // Custom spacing for sections
        'section-desktop': '96px',
        'section-mobile': '48px',
        'content-gutter': '24px',
      },
      maxWidth: {
        'content': '1280px', // Content max-width
        '8xl': '88rem', // ~1408px - Extra large for hero logo
      },
      borderRadius: {
        'none': '0px',
        'sm': '2px',
        'DEFAULT': '4px',
        'md': '6px',
        'lg': '8px',
        'xl': '12px',
        '2xl': '16px',
        '3xl': '24px',
        'full': '9999px',
        // Legacy support
        'pill': '9999px',
        'card': '12px',
        'input': '8px',
        'video': '16px',
      },
      boxShadow: {
        // Makeswift-inspired shadow system
        'xs': '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        'sm': '0px 1px 3px 0px rgba(16, 24, 40, 0.1), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        'md': '0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        'lg': '0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03)',
        'xl': '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)',
        '2xl': '0px 24px 48px -12px rgba(16, 24, 40, 0.18)',
        '3xl': '0px 32px 64px -12px rgba(16, 24, 40, 0.14)',
        // Legacy support
        'button': '0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        'button-hover': '0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03)',
        'card': '0px 1px 3px 0px rgba(16, 24, 40, 0.1), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        'overlay': '0px 24px 48px -12px rgba(16, 24, 40, 0.18)',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
        'gradient-accent': 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
        'gradient-overlay': 'linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4))',
        'gradient-subtle': 'linear-gradient(135deg, #F9FAFB 0%, #F2F4F7 100%)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s infinite',
        'float': 'float 6s ease-in-out infinite',
        'scroll': 'scroll 30s linear infinite',
        // Hyper Component Animations
        'hyper-scroll': 'hyper-scroll 45s linear infinite',
        'scan': 'scan 2s ease-in-out infinite',
        'ripple': 'ripple 0.6s ease-out forwards',
        'quantum-rotation': 'quantum-rotation 4s ease-in-out infinite',
        'plasma-pulse': 'plasma-pulse 2s ease-in-out infinite',
        'holographic-shift': 'holographic-shift 3s ease-in-out infinite',
        'energy-sweep': 'energy-sweep 2s ease-in-out infinite',
        'particle-float': 'particle-float 3s ease-in-out infinite',
        // Enhanced Glass Animations
        'glass-shimmer': 'glass-shimmer 3s ease-in-out infinite',
        'chromatic-shift': 'chromatic-shift 4s ease-in-out infinite',
        // Advanced Neumorphism Animations
        'neuro-light-rotation': 'neuro-light-rotation 8s linear infinite',
        'shadow-morph': 'shadow-morph 6s ease-in-out infinite',
        'neuro-shine': 'neuro-shine 4s ease-in-out infinite',
        // Advanced Micro-Interaction Animations
        'sound-ripple': 'sound-ripple 0.6s ease-out',
        'context-glow': 'context-glow 2s ease-in-out infinite',
        'magnetic-pull': 'magnetic-pull 0.3s ease-out',
        'particle-burst': 'particle-burst 0.6s ease-out',
        'glow-pulse-interaction': 'glow-pulse-interaction 1s ease-in-out infinite',
        // About Page Specific Animations
        'slide-up-fade': 'slide-up-fade 0.8s ease-out forwards',
        'float-slow': 'float-slow 4s ease-in-out infinite',
        'gradient-x': 'gradient-x 3s ease infinite',
        // OPTIMIZED: Removed expensive infinite animations for performance
        // 'ambient-pulse': 'ambient-pulse 8s ease-in-out infinite', // REMOVED
        // 'ambient-glow-pulse': 'ambient-glow-pulse 4s ease-in-out infinite', // REMOVED
        // 'edge-light-shift': 'edge-light-shift 12s ease-in-out infinite', // REMOVED
        // Revolutionary 3D Transform Animations - SIMPLIFIED
        // 'rotate-3d-x': 'rotate-3d-x 10s linear infinite', // REMOVED
        // 'rotate-3d-y': 'rotate-3d-y 8s linear infinite', // REMOVED
        'rotate-3d-z': 'rotate-3d-z 6s linear infinite', // KEPT - used sparingly
        'rotate-3d-complex': 'rotate-3d-complex 12s ease-in-out infinite', // KEPT - used sparingly
        // 'morph-3d-shape': 'morph-3d-shape 8s ease-in-out infinite', // REMOVED
        // Dynamic Gradient Animations - REMOVED FOR PERFORMANCE
        // 'gradient-mesh-flow': 'gradient-mesh-flow 20s ease-in-out infinite', // REMOVED
        // 'gradient-noise-shift': 'gradient-noise-shift 15s ease-in-out infinite', // REMOVED
        // 'noise-pattern': 'noise-pattern 8s linear infinite', // REMOVED
        'gradient-bleeding-rotation': 'gradient-bleeding-rotation 12s linear infinite',
        'gradient-liquid-flow': 'gradient-liquid-flow 18s ease-in-out infinite',
        'gradient-plasma-pulse': 'gradient-plasma-pulse 16s ease-in-out infinite',
        'gradient-holographic-shift': 'gradient-holographic-shift 14s ease-in-out infinite',
        'gradient-aurora-wave': 'gradient-aurora-wave 25s ease-in-out infinite',
        'gradient-chromatic-spin': 'gradient-chromatic-spin 20s linear infinite',
        'gradient-distortion-warp': 'gradient-distortion-warp 22s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-50%)' },
        },
        // 2025 Modern Keyframes
        glow: {
          '0%': {
            boxShadow: '0 0 20px rgba(255, 0, 128, 0.5), 0 0 40px rgba(0, 255, 255, 0.3)',
            filter: 'brightness(1)'
          },
          '100%': {
            boxShadow: '0 0 40px rgba(255, 0, 128, 0.8), 0 0 80px rgba(0, 255, 255, 0.6)',
            filter: 'brightness(1.2)'
          },
        },
        'gradient-x': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        'gradient-y': {
          '0%, 100%': { backgroundPosition: '50% 0%' },
          '50%': { backgroundPosition: '50% 100%' },
        },
        'pulse-glow': {
          '0%, 100%': {
            opacity: '1',
            boxShadow: '0 0 20px rgba(255, 0, 128, 0.4)'
          },
          '50%': {
            opacity: '0.8',
            boxShadow: '0 0 40px rgba(255, 0, 128, 0.8), 0 0 60px rgba(0, 255, 255, 0.4)'
          },
        },
        'slide-up-fade': {
          '0%': {
            opacity: '0',
            transform: 'translateY(60px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
        },
        'scale-in-bounce': {
          '0%': {
            opacity: '0',
            transform: 'scale(0.3)'
          },
          '50%': {
            transform: 'scale(1.05)'
          },
          '70%': {
            transform: 'scale(0.9)'
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)'
          },
        },
        // Hyper Component Animations
        'hyper-scroll': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-33.333%)' },
        },
        'scan': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'ripple': {
          '0%': {
            width: '0',
            height: '0',
            opacity: '1'
          },
          '100%': {
            width: '200px',
            height: '200px',
            opacity: '0'
          },
        },
        'quantum-rotation': {
          '0%, 100%': { transform: 'rotateY(0deg) rotateX(0deg)' },
          '25%': { transform: 'rotateY(90deg) rotateX(5deg)' },
          '50%': { transform: 'rotateY(180deg) rotateX(0deg)' },
          '75%': { transform: 'rotateY(270deg) rotateX(-5deg)' },
        },
        'plasma-pulse': {
          '0%, 100%': {
            transform: 'scale(1)',
            filter: 'brightness(1) saturate(1)'
          },
          '50%': {
            transform: 'scale(1.05)',
            filter: 'brightness(1.2) saturate(1.3)'
          },
        },
        'holographic-shift': {
          '0%, 100%': {
            background: 'linear-gradient(45deg, #06b6d4, #3b82f6, #8b5cf6)',
            transform: 'translateZ(0)'
          },
          '33%': {
            background: 'linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4)',
            transform: 'translateZ(5px)'
          },
          '66%': {
            background: 'linear-gradient(45deg, #8b5cf6, #06b6d4, #3b82f6)',
            transform: 'translateZ(-5px)'
          },
        },
        'energy-sweep': {
          '0%': { left: '-100%' },
          '100%': { left: '100%' },
        },
        'particle-float': {
          '0%, 100%': {
            transform: 'translateY(0) translateX(0) scale(1)',
            opacity: '0.3'
          },
          '50%': {
            transform: 'translateY(-20px) translateX(10px) scale(1.2)',
            opacity: '1'
          },
        },
        // Enhanced Glass Effects
        'glass-shimmer': {
          '0%': { left: '-100%' },
          '50%': { left: '100%' },
          '100%': { left: '100%' },
        },
        'chromatic-shift': {
          '0%, 100%': {
            filter: 'hue-rotate(0deg) saturate(100%)',
            transform: 'translateX(0) translateY(0)'
          },
          '25%': {
            filter: 'hue-rotate(90deg) saturate(150%)',
            transform: 'translateX(1px) translateY(-1px)'
          },
          '50%': {
            filter: 'hue-rotate(180deg) saturate(120%)',
            transform: 'translateX(-1px) translateY(1px)'
          },
          '75%': {
            filter: 'hue-rotate(270deg) saturate(180%)',
            transform: 'translateX(1px) translateY(1px)'
          },
        },
        // Advanced Neumorphism Effects
        'neuro-light-rotation': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'shadow-morph': {
          '0%, 100%': {
            boxShadow: '20px 20px 40px rgba(0, 0, 0, 0.8), -20px -20px 40px rgba(255, 255, 255, 0.05)'
          },
          '25%': {
            boxShadow: '30px 10px 50px rgba(0, 0, 0, 0.9), -30px -10px 50px rgba(255, 255, 255, 0.06)'
          },
          '50%': {
            boxShadow: '10px 30px 60px rgba(0, 0, 0, 0.85), -10px -30px 60px rgba(255, 255, 255, 0.04)'
          },
          '75%': {
            boxShadow: '40px 20px 45px rgba(0, 0, 0, 0.75), -40px -20px 45px rgba(255, 255, 255, 0.07)'
          },
        },
        'neuro-shine': {
          '0%': { left: '-100%' },
          '50%': { left: '100%' },
          '100%': { left: '100%' },
        },
        // Advanced Micro-Interaction Effects
        'sound-ripple': {
          '0%': {
            width: '0',
            height: '0',
            opacity: '1'
          },
          '100%': {
            width: '200px',
            height: '200px',
            opacity: '0'
          },
        },
        'context-glow': {
          '0%, 100%': {
            filter: 'blur(4px) brightness(1)',
            transform: 'scale(1)'
          },
          '50%': {
            filter: 'blur(8px) brightness(1.2)',
            transform: 'scale(1.02)'
          },
        },
        'magnetic-pull': {
          '0%': { transform: 'scale(1) rotate(0deg)' },
          '50%': { transform: 'scale(1.05) rotate(2deg)' },
          '100%': { transform: 'scale(1.02) rotate(0deg)' },
        },
        'particle-burst': {
          '0%': {
            opacity: '1',
            transform: 'translate(-50%, -50%) scale(1)',
            boxShadow: '0 0 0 0 rgba(255, 0, 128, 0.8)'
          },
          '100%': {
            opacity: '0',
            transform: 'translate(-50%, -50%) scale(20)',
            boxShadow: '0 0 0 20px rgba(255, 0, 128, 0)'
          },
        },
        'glow-pulse-interaction': {
          '0%, 100%': {
            filter: 'brightness(1) saturate(1)',
            transform: 'scale(1)'
          },
          '50%': {
            filter: 'brightness(1.1) saturate(1.2)',
            transform: 'scale(1.01)'
          },
        },
        // Enhanced Dark Mode Effects
        'ambient-pulse': {
          '0%, 100%': {
            opacity: '0.8',
            transform: 'scale(1)'
          },
          '50%': {
            opacity: '1',
            transform: 'scale(1.02)'
          },
        },
        'ambient-glow-pulse': {
          '0%, 100%': {
            opacity: '0.6',
            filter: 'blur(20px)'
          },
          '50%': {
            opacity: '1',
            filter: 'blur(30px)'
          },
        },
        'edge-light-shift': {
          '0%, 100%': {
            filter: 'hue-rotate(0deg) brightness(1)'
          },
          '25%': {
            filter: 'hue-rotate(90deg) brightness(1.1)'
          },
          '50%': {
            filter: 'hue-rotate(180deg) brightness(0.9)'
          },
          '75%': {
            filter: 'hue-rotate(270deg) brightness(1.05)'
          },
        },
        // Revolutionary 3D Transform Effects
        // REMOVED: rotate-3d-x and rotate-3d-y keyframes for performance
        'rotate-3d-z': {
          '0%': { transform: 'rotateZ(0deg)' },
          '100%': { transform: 'rotateZ(360deg)' },
        },
        'rotate-3d-complex': {
          '0%': {
            transform: 'rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateZ(0px)'
          },
          '25%': {
            transform: 'rotateX(90deg) rotateY(90deg) rotateZ(0deg) translateZ(20px)'
          },
          '50%': {
            transform: 'rotateX(180deg) rotateY(180deg) rotateZ(180deg) translateZ(0px)'
          },
          '75%': {
            transform: 'rotateX(270deg) rotateY(270deg) rotateZ(180deg) translateZ(-20px)'
          },
          '100%': {
            transform: 'rotateX(360deg) rotateY(360deg) rotateZ(360deg) translateZ(0px)'
          },
        },
        'morph-3d-shape': {
          '0%, 100%': {
            transform: 'rotateX(0deg) rotateY(0deg) scale(1)',
            borderRadius: '20px'
          },
          '25%': {
            transform: 'rotateX(45deg) rotateY(45deg) scale(1.1)',
            borderRadius: '50%'
          },
          '50%': {
            transform: 'rotateX(90deg) rotateY(90deg) scale(0.9)',
            borderRadius: '10px'
          },
          '75%': {
            transform: 'rotateX(135deg) rotateY(135deg) scale(1.05)',
            borderRadius: '30px'
          },
        },
        // Dynamic Gradient Animation Effects
        'gradient-mesh-flow': {
          '0%, 100%': {
            backgroundPosition: '0% 0%, 100% 100%, 50% 50%, 25% 75%, 0% 0%'
          },
          '25%': {
            backgroundPosition: '25% 25%, 75% 25%, 75% 25%, 50% 50%, 0% 0%'
          },
          '50%': {
            backgroundPosition: '50% 50%, 50% 50%, 25% 75%, 75% 25%, 0% 0%'
          },
          '75%': {
            backgroundPosition: '75% 75%, 25% 75%, 0% 100%, 100% 0%, 0% 0%'
          },
        },
        'gradient-noise-shift': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        'noise-pattern': {
          '0%': { transform: 'translateX(0) translateY(0)' },
          '100%': { transform: 'translateX(4px) translateY(4px)' },
        },
        'gradient-bleeding-rotation': {
          '0%': { transform: 'rotate(0deg) scale(1)' },
          '100%': { transform: 'rotate(360deg) scale(1)' },
        },
        'gradient-liquid-flow': {
          '0%, 100%': {
            backgroundPosition: '0% 50%',
            filter: 'blur(0.5px) contrast(120%) saturate(130%)'
          },
          '25%': {
            backgroundPosition: '50% 0%',
            filter: 'blur(1px) contrast(110%) saturate(140%)'
          },
          '50%': {
            backgroundPosition: '100% 50%',
            filter: 'blur(0.8px) contrast(130%) saturate(120%)'
          },
          '75%': {
            backgroundPosition: '50% 100%',
            filter: 'blur(0.3px) contrast(125%) saturate(135%)'
          },
        },
        'gradient-plasma-pulse': {
          '0%, 100%': {
            filter: 'blur(2px) brightness(100%) saturate(100%)',
            transform: 'scale(1)'
          },
          '50%': {
            filter: 'blur(4px) brightness(120%) saturate(150%)',
            transform: 'scale(1.02)'
          },
        },
        'gradient-holographic-shift': {
          '0%, 100%': {
            backgroundPosition: '0% 50%',
            filter: 'hue-rotate(0deg) saturate(150%) brightness(110%)'
          },
          '25%': {
            backgroundPosition: '25% 25%',
            filter: 'hue-rotate(90deg) saturate(180%) brightness(120%)'
          },
          '50%': {
            backgroundPosition: '100% 50%',
            filter: 'hue-rotate(180deg) saturate(200%) brightness(100%)'
          },
          '75%': {
            backgroundPosition: '75% 75%',
            filter: 'hue-rotate(270deg) saturate(160%) brightness(115%)'
          },
        },
        'gradient-aurora-wave': {
          '0%, 100%': {
            backgroundPosition: '0% 50%',
            filter: 'blur(2px) brightness(120%)'
          },
          '33%': {
            backgroundPosition: '50% 0%',
            filter: 'blur(3px) brightness(110%)'
          },
          '66%': {
            backgroundPosition: '100% 50%',
            filter: 'blur(1px) brightness(130%)'
          },
        },
        'gradient-chromatic-spin': {
          '0%': {
            transform: 'rotate(0deg)',
            filter: 'saturate(200%) contrast(110%)'
          },
          '100%': {
            transform: 'rotate(360deg)',
            filter: 'saturate(200%) contrast(110%)'
          },
        },
        'gradient-distortion-warp': {
          '0%, 100%': {
            transform: 'scaleX(1) scaleY(1) skewX(0deg)',
            filter: 'blur(1px)'
          },
          '25%': {
            transform: 'scaleX(1.1) scaleY(0.9) skewX(2deg)',
            filter: 'blur(2px)'
          },
          '50%': {
            transform: 'scaleX(0.9) scaleY(1.1) skewX(-1deg)',
            filter: 'blur(1.5px)'
          },
          '75%': {
            transform: 'scaleX(1.05) scaleY(0.95) skewX(1deg)',
            filter: 'blur(0.5px)'
          },
        },
      },
      aspectRatio: {
        'video': '16 / 9',
      },
    },
  },
  plugins: [],
}
