'use client';

import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import Logo from './Logo';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const [scrollY, setScrollY] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [hoveredSocial, setHoveredSocial] = useState<string | null>(null);
  const [time, setTime] = useState(0);
  const footerRef = useRef<HTMLElement>(null);

  const socialPlatforms = [
    {
      name: 'twitter',
      url: 'https://twitter.com/consciouscollective',
      color: 'from-blue-400 to-blue-600',
      icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z',
      orbitRadius: 80,
      orbitSpeed: 3
    },
    {
      name: 'instagram',
      url: 'https://instagram.com/consciouscollective',
      color: 'from-pink-500 to-purple-600',
      icon: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z',
      orbitRadius: 100,
      orbitSpeed: 4
    },
    {
      name: 'tiktok',
      url: 'https://tiktok.com/@consciouscollective',
      color: 'from-red-500 to-pink-500',
      icon: 'M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z',
      orbitRadius: 120,
      orbitSpeed: 5
    },
    {
      name: 'spotify',
      url: 'https://open.spotify.com/user/consciouscollective',
      color: 'from-green-400 to-green-600',
      icon: 'M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z',
      orbitRadius: 90,
      orbitSpeed: 3.5
    }
  ];

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    const handleMouseMove = (e: MouseEvent) => {
      if (footerRef.current) {
        const rect = footerRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    // Optimized animation timer using requestAnimationFrame
    let animationId: number;
    let startTime = Date.now();

    const animate = () => {
      if (document.hidden) return; // Pause when tab is hidden

      const elapsed = (Date.now() - startTime) / 1000;
      setTime(elapsed);
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return (
    <footer
      ref={footerRef}
      className="relative overflow-hidden bg-black border-t border-white/10"
      style={{
        background: `
          radial-gradient(circle at 20% 50%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
          linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 1) 100%)
        `
      }}
    >
      {/* Ultra-Dynamic Liquid Background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Morphing Liquid Blobs */}
        <div
          className="absolute w-96 h-96 rounded-full opacity-20 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(255, 0, 128, 0.4) 0%, transparent 70%)',
            left: `${20 + Math.sin(time * 0.5 + scrollY * 0.01) * 15}%`,
            top: `${30 + Math.cos(time * 0.3 + scrollY * 0.008) * 20}%`,
            transform: `scale(${1 + Math.sin(time * 0.4) * 0.3}) rotate(${time * 10}deg)`,
          }}
        />
        <div
          className="absolute w-80 h-80 rounded-full opacity-15 blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(0, 255, 255, 0.4) 0%, transparent 70%)',
            right: `${15 + Math.cos(time * 0.7 + scrollY * 0.012) * 12}%`,
            bottom: `${20 + Math.sin(time * 0.6 + scrollY * 0.009) * 18}%`,
            transform: `scale(${1 + Math.cos(time * 0.5) * 0.2}) rotate(${-time * 8}deg)`,
          }}
        />

        {/* Quantum Field Visualization */}
        <div className="absolute inset-0 opacity-10">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="quantum" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
                <circle
                  cx="60"
                  cy="60"
                  r={3 + Math.sin(time * 2) * 1}
                  fill="rgba(255, 255, 255, 0.4)"
                />
                <line
                  x1="60"
                  y1="60"
                  x2={60 + Math.cos(time) * 40}
                  y2={60 + Math.sin(time) * 40}
                  stroke="rgba(255, 255, 255, 0.2)"
                  strokeWidth="1"
                />
                <line
                  x1="60"
                  y1="60"
                  x2={60 + Math.cos(time + Math.PI) * 30}
                  y2={60 + Math.sin(time + Math.PI) * 30}
                  stroke="rgba(0, 255, 255, 0.2)"
                  strokeWidth="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#quantum)" />
          </svg>
        </div>
      </div>

      <div className="content-container py-24 relative z-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-20 mb-24">
          {/* Company Info with Holographic Effect */}
          <div className="md:col-span-1 relative">
            <Logo variant="footer" size="lg" showText={false} href="/" className="mb-10" />

            <p className="text-white/70 text-lg font-medium leading-relaxed mb-12 hover:text-white/90 transition-colors duration-300">
              Manifesting dance floor euphoria across San Diego since 2024. Join the movement.
            </p>

            {/* Orbital Social Icons System */}
            <div className="relative w-64 h-64 mx-auto">
              {/* Central Hub */}
              <div className="absolute top-1/2 left-1/2 w-16 h-16 -translate-x-1/2 -translate-y-1/2 z-20">
                <div className="w-full h-full bg-gradient-to-r from-neon-pink via-neon-purple to-neon-cyan rounded-full flex items-center justify-center animate-pulse-glow shadow-2xl">
                  <span className="text-black font-black text-sm">SOCIAL</span>
                </div>
                {/* Energy Pulse */}
                <div className="absolute inset-0 rounded-full border-2 border-white/30 animate-ping" />
              </div>

              {/* Orbital Social Icons */}
              {socialPlatforms.map((platform, index) => {
                const angle = (time * platform.orbitSpeed + index * (Math.PI * 2 / socialPlatforms.length)) % (Math.PI * 2);
                const x = Math.cos(angle) * platform.orbitRadius;
                const y = Math.sin(angle) * platform.orbitRadius;

                return (
                  <div
                    key={platform.name}
                    className="absolute top-1/2 left-1/2 z-10"
                    style={{
                      transform: `translate(${x - 24}px, ${y - 24}px)`,
                    }}
                  >
                    <a
                      href={platform.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group relative block w-12 h-12"
                      onMouseEnter={() => setHoveredSocial(platform.name)}
                      onMouseLeave={() => setHoveredSocial(null)}
                      aria-label={`Follow us on ${platform.name}`}
                    >
                      {/* Icon Container */}
                      <div className={`w-full h-full glass rounded-xl flex items-center justify-center group-hover:scale-125 transition-all duration-500 border border-white/20 group-hover:border-white/40`}>
                        <svg className="w-5 h-5 text-white group-hover:text-black transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                          <path d={platform.icon} />
                        </svg>
                      </div>

                      {/* Hover Glow Effect */}
                      <div className={`absolute inset-0 bg-gradient-to-r ${platform.color} rounded-xl opacity-0 group-hover:opacity-80 transition-opacity duration-500 blur-sm`} />

                      {/* Magnetic Trail */}
                      {hoveredSocial === platform.name && (
                        <div className="absolute inset-0 rounded-xl border-2 border-white/60 animate-ping" />
                      )}

                      {/* Platform Label */}
                      <div className="absolute -bottom-8 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <span className="text-xs font-bold text-white uppercase tracking-wider bg-black/50 px-2 py-1 rounded-lg backdrop-blur-sm">
                          {platform.name}
                        </span>
                      </div>
                    </a>

                    {/* Orbital Trail */}
                    <div
                      className="absolute top-1/2 left-1/2 w-1 h-1 bg-white/20 rounded-full"
                      style={{
                        transform: `translate(-50%, -50%)`,
                        boxShadow: `0 0 20px rgba(255, 255, 255, 0.3)`,
                      }}
                    />
                  </div>
                );
              })}

              {/* Orbital Rings */}
              {[80, 100, 120].map((radius, index) => (
                <div
                  key={radius}
                  className="absolute top-1/2 left-1/2 border border-white/10 rounded-full pointer-events-none"
                  style={{
                    width: radius * 2,
                    height: radius * 2,
                    transform: 'translate(-50%, -50%)',
                    opacity: hoveredSocial ? 0.3 : 0.1,
                    transition: 'opacity 0.5s ease',
                  }}
                />
              ))}
            </div>
          </div>

          {/* Events & Experiences - Holographic Menu */}
          <div className="relative">
            <div className="glass rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500 group">
              <h3 className="text-white font-black text-xl mb-8 uppercase tracking-wide group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-neon-pink group-hover:to-neon-cyan transition-all duration-500">
                Events
              </h3>
              <ul className="space-y-5">
                {[
                  { name: 'Upcoming Events', href: '/events', color: 'neon-pink' },
                  { name: 'Experiences', href: '/experiences', color: 'neon-cyan' },
                  { name: 'Venues', href: '/venues', color: 'neon-yellow' },
                  { name: 'Past Events', href: '/past-events', color: 'neon-green' },
                  { name: 'Get Tickets', href: '/tickets', color: 'neon-purple' }
                ].map((item, index) => (
                  <li key={item.name} className="group/item">
                    <Link
                      href={item.href}
                      className="relative inline-flex items-center text-white/70 hover:text-white text-base font-medium transition-all duration-300 group-hover/item:translate-x-2"
                    >
                      {/* Animated Bullet */}
                      <div className={`w-2 h-2 bg-${item.color} rounded-full mr-4 opacity-0 group-hover/item:opacity-100 transition-all duration-300 animate-pulse-glow`} />

                      {/* Holographic Underline */}
                      <span className="relative">
                        {item.name}
                        <div className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-${item.color} to-transparent group-hover/item:w-full transition-all duration-500`} />
                      </span>

                      {/* Energy Arrow */}
                      <svg
                        className="w-4 h-4 ml-2 opacity-0 group-hover/item:opacity-100 group-hover/item:translate-x-1 transition-all duration-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Community & Info - Neural Network Style */}
          <div className="relative">
            <div className="glass rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500 group">
              <h3 className="text-white font-black text-xl mb-8 uppercase tracking-wide group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-neon-cyan group-hover:to-neon-green transition-all duration-500">
                Community
              </h3>
              <ul className="space-y-5">
                {[
                  { name: 'About Us', href: '/about', color: 'neon-pink' },
                  { name: 'Join Collective', href: '/collective', color: 'neon-cyan' },
                  { name: 'Contact', href: '/contact', color: 'neon-yellow' },
                  { name: 'Newsletter', href: '/newsletter', color: 'neon-green' },
                  { name: 'FAQ', href: '/faq', color: 'neon-purple' }
                ].map((item, index) => (
                  <li key={item.name} className="group/item">
                    <Link
                      href={item.href}
                      className="relative inline-flex items-center text-white/70 hover:text-white text-base font-medium transition-all duration-300 group-hover/item:translate-x-2"
                    >
                      {/* Neural Node */}
                      <div className={`w-2 h-2 bg-${item.color} rounded-full mr-4 opacity-0 group-hover/item:opacity-100 transition-all duration-300 animate-pulse-glow`} />

                      {/* Quantum Underline */}
                      <span className="relative">
                        {item.name}
                        <div className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-${item.color} to-transparent group-hover/item:w-full transition-all duration-500`} />
                      </span>

                      {/* Data Stream Arrow */}
                      <svg
                        className="w-4 h-4 ml-2 opacity-0 group-hover/item:opacity-100 group-hover/item:translate-x-1 transition-all duration-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Ultra-Modern Bottom Section */}
        <div className="relative">
          {/* Separator with Energy Flow */}
          <div className="relative mb-12">
            <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-pink opacity-50 animate-gradient-x" />

            {/* Energy Nodes */}
            <div className="absolute top-0 left-1/4 w-2 h-2 bg-neon-pink rounded-full -translate-y-1/2 animate-pulse-glow" />
            <div className="absolute top-0 left-1/2 w-3 h-3 bg-neon-cyan rounded-full -translate-y-1/2 animate-pulse-glow" />
            <div className="absolute top-0 left-3/4 w-2 h-2 bg-neon-purple rounded-full -translate-y-1/2 animate-pulse-glow" />
          </div>

          <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0">
            {/* Copyright with Holographic Effect */}
            <div className="group">
              <p className="text-white/60 text-sm font-medium group-hover:text-white/80 transition-colors duration-300">
                &copy; {currentYear} Conscious Collective, Inc.
                <span className="ml-2 text-neon-cyan animate-pulse-glow">●</span>
                <span className="ml-1 text-xs">LIVE</span>
              </p>
            </div>

            {/* Legal Links with Quantum Effects */}
            <div className="flex items-center space-x-8">
              {[
                { name: 'Privacy', href: '/privacy' },
                { name: 'Terms', href: '/terms' },
                { name: 'Cookies', href: '/cookies' }
              ].map((link, index) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="relative group text-white/60 hover:text-white text-sm font-medium transition-all duration-300"
                >
                  <span className="relative z-10">{link.name}</span>

                  {/* Quantum Glow */}
                  <div className="absolute inset-0 bg-gradient-to-r from-neon-pink/20 to-neon-cyan/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />

                  {/* Underline Animation */}
                  <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-neon-pink to-neon-cyan group-hover:w-full transition-all duration-500" />
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Mouse Follower Effect */}
        {hoveredSocial && (
          <div
            className="fixed pointer-events-none z-50 w-40 h-40 rounded-full opacity-20 blur-3xl transition-all duration-300"
            style={{
              background: `radial-gradient(circle, ${socialPlatforms.find(p => p.name === hoveredSocial)?.color.split(' ')[1]} 0%, transparent 70%)`,
              left: mousePosition.x - 80,
              top: mousePosition.y - 80,
              transform: 'translate(-50%, -50%)',
            }}
          />
        )}
      </div>
    </footer>
  );
}
