/**
 * Global Performance Manager
 * Coordinates performance-ready events and prevents React Strict Mode issues
 */

import React from 'react';

class GlobalPerformanceManager {
  private static instance: GlobalPerformanceManager;
  private hasInitialized = false;
  private performanceReadyFired = false;
  private listeners: Set<() => void> = new Set();
  private fallbackTimer: NodeJS.Timeout | null = null;

  private constructor() {
    // Singleton pattern
  }

  public static getInstance(): GlobalPerformanceManager {
    if (!GlobalPerformanceManager.instance) {
      GlobalPerformanceManager.instance = new GlobalPerformanceManager();
    }
    return GlobalPerformanceManager.instance;
  }

  public initialize(): void {
    // Prevent double initialization in React Strict Mode
    if (this.hasInitialized) {
      console.log('PerformanceManager: Already initialized, skipping');
      return;
    }

    this.hasInitialized = true;
    console.log('PerformanceManager: Initializing...');

    this.optimizePageLoad();
  }

  private async optimizePageLoad(): Promise<void> {
    try {
      // Wait for DOM to be fully ready
      await new Promise<void>(resolve => {
        if (document.readyState === 'complete') {
          resolve();
        } else {
          window.addEventListener('load', () => resolve(), { once: true });
        }
      });

      // Add a delay to prevent simultaneous animation starts
      await new Promise<void>(resolve => setTimeout(resolve, 150));

      this.firePerformanceReady();
    } catch (error) {
      console.error('PerformanceManager: Error during page load optimization:', error);
      // Fallback: fire performance ready anyway
      this.firePerformanceReady();
    }
  }

  private firePerformanceReady(): void {
    if (this.performanceReadyFired) {
      console.log('PerformanceManager: performance-ready already fired, skipping');
      return;
    }

    this.performanceReadyFired = true;
    console.log('PerformanceManager: Firing performance-ready event');

    // Add CSS class for animations
    document.body.classList.add('animations-ready');

    // Notify all registered listeners
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('PerformanceManager: Error in listener:', error);
      }
    });

    // Fire global event for legacy compatibility
    window.dispatchEvent(new CustomEvent('performance-ready'));

    // Clear fallback timer if it exists
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }
  }

  public onPerformanceReady(callback: () => void): () => void {
    // If performance ready already fired, call immediately
    if (this.performanceReadyFired) {
      console.log('PerformanceManager: performance-ready already fired, calling callback immediately');
      setTimeout(callback, 0);
      return () => {}; // No-op cleanup
    }

    // Add to listeners
    this.listeners.add(callback);

    // Set up fallback timer if this is the first listener
    if (this.listeners.size === 1 && !this.fallbackTimer) {
      this.fallbackTimer = setTimeout(() => {
        console.log('PerformanceManager: Fallback timer triggered');
        this.firePerformanceReady();
      }, 500);
    }

    // Return cleanup function
    return () => {
      this.listeners.delete(callback);
    };
  }

  public isReady(): boolean {
    return this.performanceReadyFired;
  }

  public reset(): void {
    // For testing purposes only
    this.hasInitialized = false;
    this.performanceReadyFired = false;
    this.listeners.clear();
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }
  }
}

// Export singleton instance
export const performanceManager = GlobalPerformanceManager.getInstance();

// Convenience hook for React components
export function usePerformanceReady(callback: () => void): void {
  React.useEffect(() => {
    const cleanup = performanceManager.onPerformanceReady(callback);
    return cleanup;
  }, [callback]);
}

// For backwards compatibility
export function initializePerformanceManager(): void {
  performanceManager.initialize();
}
