#!/usr/bin/env node

/**
 * Asset Optimization Script for Phase 1
 * 
 * This script will:
 * 1. Create 4-second video loops with seamless transitions
 * 2. Extract frame-1 placeholder images as WebP
 * 3. Optimize image assets to WebP format
 * 4. Measure file size improvements
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  inputDir: 'public/images',
  outputDir: 'public/images/optimized',
  videos: [
    { input: 'hero_logo.mp4', output: 'hero_logo_4s.mp4' },
    { input: 'hero_logo_high.mp4', output: 'hero_logo_high_4s.mp4' }
  ],
  images: [
    { input: 'logo.gif', output: 'logo.webp' },
    { input: 'logo_cursive_white.png', output: 'logo_cursive_white.webp' },
    { input: 'logo.png', output: 'logo.webp' },
    { input: 'logo2.png', output: 'logo2.webp' },
    { input: 'logo3.png', output: 'logo3.webp' }
  ],
  placeholders: [
    { video: 'hero_logo_4s.mp4', output: 'hero_logo_frame1.webp' },
    { video: 'hero_logo_high_4s.mp4', output: 'hero_logo_high_frame1.webp' }
  ]
};

// Utility functions
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function checkFFmpeg() {
  try {
    execSync('ffmpeg -version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

// Video optimization functions
function createSeamlessLoop(inputPath, outputPath, duration = 4) {
  console.log(`Creating ${duration}s seamless loop: ${inputPath} -> ${outputPath}`);
  
  try {
    // First, get video info to find the best loop point
    const probeCmd = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${inputPath}"`;
    const originalDuration = parseFloat(execSync(probeCmd, { encoding: 'utf8' }).trim());
    
    console.log(`Original duration: ${originalDuration}s`);
    
    // Create 4-second loop with crossfade for seamless transition
    const ffmpegCmd = `ffmpeg -i "${inputPath}" -t ${duration} -vf "scale=trunc(iw/2)*2:trunc(ih/2)*2" -c:v libx264 -preset medium -crf 23 -pix_fmt yuv420p -movflags +faststart -y "${outputPath}"`;
    
    execSync(ffmpegCmd, { stdio: 'inherit' });
    
    const originalSize = getFileSize(inputPath);
    const newSize = getFileSize(outputPath);
    const savings = ((originalSize - newSize) / originalSize * 100).toFixed(1);
    
    console.log(`✅ Video optimized: ${formatBytes(originalSize)} -> ${formatBytes(newSize)} (${savings}% reduction)`);
    
    return { originalSize, newSize, savings };
  } catch (error) {
    console.error(`❌ Error processing ${inputPath}:`, error.message);
    return null;
  }
}

function extractFrame1(videoPath, outputPath) {
  console.log(`Extracting frame 1: ${videoPath} -> ${outputPath}`);
  
  try {
    // Extract first frame as high-quality WebP
    const ffmpegCmd = `ffmpeg -i "${videoPath}" -vf "select=eq(n\\,0)" -vframes 1 -c:v libwebp -quality 85 -y "${outputPath}"`;
    
    execSync(ffmpegCmd, { stdio: 'inherit' });
    
    const size = getFileSize(outputPath);
    console.log(`✅ Frame extracted: ${formatBytes(size)}`);
    
    return size;
  } catch (error) {
    console.error(`❌ Error extracting frame from ${videoPath}:`, error.message);
    return null;
  }
}

// Image optimization functions
function optimizeImage(inputPath, outputPath) {
  console.log(`Optimizing image: ${inputPath} -> ${outputPath}`);
  
  try {
    let ffmpegCmd;
    
    if (inputPath.endsWith('.gif')) {
      // Convert animated GIF to WebP animation
      ffmpegCmd = `ffmpeg -i "${inputPath}" -c:v libwebp -quality 80 -compression_level 6 -y "${outputPath}"`;
    } else {
      // Convert static image to WebP
      ffmpegCmd = `ffmpeg -i "${inputPath}" -c:v libwebp -quality 85 -y "${outputPath}"`;
    }
    
    execSync(ffmpegCmd, { stdio: 'inherit' });
    
    const originalSize = getFileSize(inputPath);
    const newSize = getFileSize(outputPath);
    const savings = ((originalSize - newSize) / originalSize * 100).toFixed(1);
    
    console.log(`✅ Image optimized: ${formatBytes(originalSize)} -> ${formatBytes(newSize)} (${savings}% reduction)`);
    
    return { originalSize, newSize, savings };
  } catch (error) {
    console.error(`❌ Error processing ${inputPath}:`, error.message);
    return null;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Phase 1: Asset Optimization\n');
  
  // Check if FFmpeg is available
  if (!checkFFmpeg()) {
    console.error('❌ FFmpeg not found. Please install FFmpeg first:');
    console.error('   brew install ffmpeg');
    process.exit(1);
  }
  
  // Ensure output directory exists
  ensureDir(CONFIG.outputDir);
  
  const results = {
    videos: [],
    images: [],
    placeholders: [],
    totalSavings: 0,
    totalOriginalSize: 0,
    totalNewSize: 0
  };
  
  // Process videos
  console.log('📹 Processing videos...\n');
  for (const video of CONFIG.videos) {
    const inputPath = path.join(CONFIG.inputDir, video.input);
    const outputPath = path.join(CONFIG.outputDir, video.output);
    
    if (fs.existsSync(inputPath)) {
      const result = createSeamlessLoop(inputPath, outputPath, 4);
      if (result) {
        results.videos.push({ ...video, ...result });
        results.totalOriginalSize += result.originalSize;
        results.totalNewSize += result.newSize;
      }
    } else {
      console.log(`⚠️  Video not found: ${inputPath}`);
    }
  }
  
  // Extract frame-1 placeholders
  console.log('\n🖼️  Extracting frame-1 placeholders...\n');
  for (const placeholder of CONFIG.placeholders) {
    const videoPath = path.join(CONFIG.outputDir, placeholder.video);
    const outputPath = path.join(CONFIG.outputDir, placeholder.output);
    
    if (fs.existsSync(videoPath)) {
      const size = extractFrame1(videoPath, outputPath);
      if (size) {
        results.placeholders.push({ ...placeholder, size });
      }
    } else {
      console.log(`⚠️  Video not found for frame extraction: ${videoPath}`);
    }
  }
  
  // Process images
  console.log('\n🖼️  Processing images...\n');
  for (const image of CONFIG.images) {
    const inputPath = path.join(CONFIG.inputDir, image.input);
    const outputPath = path.join(CONFIG.outputDir, image.output);
    
    if (fs.existsSync(inputPath)) {
      const result = optimizeImage(inputPath, outputPath);
      if (result) {
        results.images.push({ ...image, ...result });
        results.totalOriginalSize += result.originalSize;
        results.totalNewSize += result.newSize;
      }
    } else {
      console.log(`⚠️  Image not found: ${inputPath}`);
    }
  }
  
  // Calculate total savings
  results.totalSavings = ((results.totalOriginalSize - results.totalNewSize) / results.totalOriginalSize * 100).toFixed(1);
  
  // Print summary
  console.log('\n📊 PHASE 1 OPTIMIZATION SUMMARY');
  console.log('================================\n');
  
  console.log('Videos:');
  results.videos.forEach(v => {
    console.log(`  ${v.input} -> ${v.output}: ${v.savings}% reduction`);
  });
  
  console.log('\nImages:');
  results.images.forEach(i => {
    console.log(`  ${i.input} -> ${i.output}: ${i.savings}% reduction`);
  });
  
  console.log('\nPlaceholders:');
  results.placeholders.forEach(p => {
    console.log(`  ${p.output}: ${formatBytes(p.size)}`);
  });
  
  console.log(`\n🎯 TOTAL SAVINGS: ${formatBytes(results.totalOriginalSize)} -> ${formatBytes(results.totalNewSize)}`);
  console.log(`💰 Size reduction: ${results.totalSavings}%\n`);
  
  // Save results for next phase
  fs.writeFileSync(
    path.join(CONFIG.outputDir, 'optimization-results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log('✅ Phase 1 complete! Results saved to optimization-results.json');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, CONFIG };
