import Layout from '../components/Layout';
import dynamic from 'next/dynamic';

// Load client-only components to avoid SSR issues
const Hero = dynamic(() => import('../components/Hero'), { ssr: false });
const LogoCarousel = dynamic(() => import('../components/LogoCarousel'), { ssr: false });
const FeaturedEvents = dynamic(() => import('../components/FeaturedEvents'), { ssr: false });
const About = dynamic(() => import('../components/About'), { ssr: false });
const MerchSection = dynamic(() => import('../components/MerchSection'), { ssr: false });
const PartyWithUs = dynamic(() => import('../components/PartyWithUs'), { ssr: false });

export default function Home() {
  return (
    <Layout
      title="Conscious Collectiv - Cultivating experiences in SoCal with intention"
      description="Join Conscious Collectiv for intentional experiences, events, and community in Southern California. Beach parties, themed events, and conscious connections."
      url="https://consciouscollectiv.com"
    >
      <Hero />
      <LogoCarousel />
      <FeaturedEvents />
      <About />
      <MerchSection />
      <PartyWithUs />
    </Layout>
  );
}

// Disable static generation for this page to avoid SSR issues
export async function getServerSideProps() {
  return {
    props: {},
  };
}
