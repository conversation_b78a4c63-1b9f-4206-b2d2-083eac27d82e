@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-black text-white antialiased;
    line-height: 1.5;
    overflow-x: hidden;
  }

  /* Performance optimization: prevent animations until ready */
  body:not(.animations-ready) * {
    animation-play-state: paused !important;
    transition-duration: 0ms !important;
  }

  /* Enhanced Dark Mode 2025+ */
  @media (prefers-color-scheme: dark) {
    body {
      @apply bg-dark text-text-inverse;
      /* Adaptive brightness based on time */
      filter: brightness(var(--adaptive-brightness, 1));
      /* Color temperature shift */
      color-scheme: dark;
    }
  }

  /* OPTIMIZED: Ambient Light Simulation - removed expensive animation */
  .dark-ambient {
    background: radial-gradient(
      ellipse at center,
      rgba(255, 255, 255, 0.02) 0%,
      rgba(0, 0, 0, 0.95) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    position: fixed;
    inset: 0;
    pointer-events: none;
    z-index: -1;
    /* REMOVED: animation: ambient-pulse 8s ease-in-out infinite; */
  }

  /* Dynamic Color Temperature */
  .dark-warm {
    filter: sepia(0.1) saturate(1.1) hue-rotate(-5deg);
  }

  .dark-cool {
    filter: sepia(0.05) saturate(1.05) hue-rotate(5deg) brightness(0.95);
  }

  .dark-neutral {
    filter: contrast(1.05) brightness(1.02);
  }

  /* Typography base styles - Makeswift inspired */
  h1 {
    @apply text-display-xl md:text-display-lg font-bold text-text-primary;
    letter-spacing: -0.025em;
  }

  h2 {
    @apply text-display-md md:text-display-sm font-semibold text-text-primary;
    letter-spacing: -0.02em;
  }

  h3 {
    @apply text-display-xs md:text-xl font-semibold text-text-primary;
  }

  h4 {
    @apply text-xl md:text-lg font-semibold text-text-primary;
  }

  h5 {
    @apply text-lg md:text-base font-semibold text-text-primary;
  }

  h6 {
    @apply text-base md:text-sm font-semibold text-text-primary;
  }

  p {
    @apply text-base md:text-sm text-text-secondary;
    line-height: 1.6;
  }

  /* Link styles */
  a {
    @apply text-brand-primary hover:text-brand-secondary transition-colors duration-200;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-brand-primary ring-offset-2 ring-offset-surface;
  }
}

@layer components {
  /* Button Components - Makeswift inspired */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-white bg-gradient-primary rounded-lg shadow-sm hover:shadow-md transition-all duration-200 ease-out;
  }

  .btn-primary:hover {
    transform: translateY(-1px);
  }

  .btn-primary:active {
    transform: translateY(0);
    @apply shadow-sm;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-text-primary bg-white border border-border-primary rounded-lg shadow-sm hover:shadow-md hover:bg-gray-50 transition-all duration-200 ease-out;
  }

  .btn-secondary:hover {
    transform: translateY(-1px);
  }

  .btn-outline {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-brand-primary border border-brand-primary bg-transparent rounded-lg hover:bg-brand-primary hover:text-white transition-all duration-200 ease-out;
  }

  .btn-outline:hover {
    transform: translateY(-1px);
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-semibold text-text-secondary bg-transparent hover:bg-gray-100 hover:text-text-primary rounded-lg transition-all duration-200 ease-out;
  }

  .btn-large {
    @apply px-8 py-4 text-lg;
  }

  .btn-small {
    @apply px-4 py-2 text-sm;
  }

  /* Legacy button support - keeping for backward compatibility */
  .btn-gradient {
    @apply btn-primary;
  }

  /* Card Components - Makeswift inspired */
  .card {
    @apply bg-surface-elevated rounded-xl border border-border-primary p-6 shadow-sm hover:shadow-md transition-all duration-200;
  }

  /* Removed unused card variants - using glass components instead */

  @media (prefers-color-scheme: dark) {
    .card {
      @apply bg-gray-800 border-gray-700;
    }
  }

  /* Form Components - Makeswift inspired */
  .form-input {
    @apply w-full px-4 py-3 text-sm bg-white border border-border-primary rounded-lg placeholder:text-text-tertiary focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200;
  }

  .form-input:hover {
    @apply border-border-tertiary;
  }

  .form-input.error {
    @apply border-error focus:ring-error focus:border-error;
  }

  .form-label {
    @apply block text-sm font-medium text-text-primary mb-2;
  }

  /* Removed unused form helper classes - using inline styles instead */

  @media (prefers-color-scheme: dark) {
    .form-input {
      @apply bg-gray-800 border-gray-700 text-white;
    }
  }

  /* Video Placeholder */
  .video-placeholder {
    @apply relative bg-gray-200 rounded-video overflow-hidden aspect-video;
  }

  .video-placeholder::before {
    content: '';
    @apply absolute inset-0 bg-gradient-overlay;
  }

  .video-placeholder .play-icon {
    @apply absolute inset-0 flex items-center justify-center;
  }

  .video-placeholder .play-icon::after {
    content: '▶';
    @apply text-white text-4xl bg-black bg-opacity-40 rounded-full w-16 h-16 flex items-center justify-center;
  }

  /* Image Placeholder */
  .image-placeholder {
    @apply bg-gray-200 rounded-card flex items-center justify-center text-gray-400;
  }

  @media (prefers-color-scheme: dark) {
    .image-placeholder {
      @apply bg-gray-700 text-gray-500;
    }
  }

  /* Removed unused polaroid styles - not used in current design */

  /* Section Spacing - Makeswift inspired */
  .section-padding {
    @apply py-16 md:py-12;
  }

  .section-padding-large {
    @apply py-24 md:py-16;
  }

  .content-container {
    @apply max-w-7xl mx-auto px-6 md:px-4;
  }

  .content-container-narrow {
    @apply max-w-4xl mx-auto px-6 md:px-4;
  }

  /* Removed unused scroll chevron - using modern scroll indicators instead */

  /* Filter Chips - Makeswift inspired */
  .filter-chip {
    @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-full border border-border-primary bg-white text-text-secondary hover:bg-gray-50 hover:text-text-primary hover:border-border-tertiary transition-all duration-200;
  }

  .filter-chip.active {
    @apply bg-brand-primary text-white border-brand-primary shadow-sm;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-3 py-1 text-xs font-medium rounded-full;
  }

  .badge-primary {
    @apply badge bg-brand-primary text-white;
  }

  /* Removed unused badge variants - using HyperBadge component instead */

  /* Masonry Grid */
  .masonry-grid {
    column-count: 3;
    column-gap: 1rem;
  }

  @media (max-width: 1024px) {
    .masonry-grid {
      column-count: 2;
    }
  }

  @media (max-width: 768px) {
    .masonry-grid {
      column-count: 1;
    }
  }

  .masonry-item {
    @apply mb-4 break-inside-avoid;
  }

  /* Carousel Styles */
  .carousel-container {
    @apply flex overflow-x-auto snap-x snap-mandatory scrollbar-hide;
  }

  .carousel-item {
    @apply flex-none snap-start;
  }

  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* OPTIMIZED: 2025 Modern Animations - reduced expensive infinite animations */
  .animate-float-slow {
    animation: float 8s ease-in-out infinite;
  }

  .animate-glow {
    /* OPTIMIZED: Simplified glow without expensive animation */
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.5), 0 0 40px rgba(0, 255, 255, 0.3);
    filter: brightness(1.1);
  }

  .animate-gradient-x {
    /* OPTIMIZED: Static gradient instead of expensive animation */
    background: linear-gradient(90deg,
      rgba(255, 0, 128, 0.8) 0%,
      rgba(0, 255, 255, 0.8) 50%,
      rgba(255, 0, 128, 0.8) 100%);
  }

  .animate-gradient-y {
    /* OPTIMIZED: Static gradient instead of expensive animation */
    background: linear-gradient(180deg,
      rgba(255, 0, 128, 0.8) 0%,
      rgba(0, 255, 255, 0.8) 50%,
      rgba(255, 0, 128, 0.8) 100%);
  }

  .animate-pulse-glow {
    /* OPTIMIZED: CSS-only pulse without expensive animation */
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.6);
    transition: box-shadow 0.3s ease;
  }

  .animate-pulse-glow:hover {
    box-shadow: 0 0 30px rgba(255, 0, 128, 0.8), 0 0 50px rgba(0, 255, 255, 0.4);
  }



  .animate-scale-in-bounce {
    animation: scale-in-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  }

  /* Scroll-triggered animations */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  /* Enhanced Glassmorphism 2025+ */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
  }

  /* About Page Specific Animations */
  @keyframes slide-up-fade {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-up-fade {
    animation: slide-up-fade 0.8s ease-out forwards;
  }

  @keyframes float-slow {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float-slow {
    animation: float-slow 4s ease-in-out infinite;
  }

  @keyframes gradient-x {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-gradient-x {
    background-size: 200% 200%;
    animation: gradient-x 3s ease infinite;
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  .reduce-motion *,
  .reduce-motion *::before,
  .reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* Performance Optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
  }

  /* Multi-layer Glass with Depth */
  .glass-ultra {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.02) 100%
    );
    backdrop-filter: blur(40px) saturate(200%) brightness(1.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
  }

  .glass-ultra::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 100%
    );
    /* REMOVED: animation: glass-shimmer 3s ease-in-out infinite; */
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  /* OPTIMIZED: Hover effect for glass shimmer */
  .glass-shimmer:hover::before {
    opacity: 1;
  }

  /* Chromatic Aberration Glass */
  .glass-chromatic {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(30px) saturate(150%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
  }

  .glass-chromatic::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg,
      rgba(255, 0, 128, 0.1) 0%,
      rgba(0, 255, 255, 0.1) 50%,
      rgba(255, 255, 0, 0.1) 100%
    );
    mix-blend-mode: overlay;
    /* REMOVED: animation: chromatic-shift 4s ease-in-out infinite; */
    pointer-events: none;
  }

  /* Interactive Glass Distortion */
  .glass-interactive {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(25px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .glass-interactive:hover {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(35px) saturate(220%) brightness(1.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Frosted Glass with Noise */
  .glass-frosted {
    background: rgba(255, 255, 255, 0.06);
    backdrop-filter: blur(50px) saturate(200%) contrast(120%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
  }

  /* Ultra Premium Glass for Event Cards */
  .glass-ultra-premium {
    background:
      linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.12) 100%
      );
    backdrop-filter: blur(40px) saturate(200%) contrast(120%) brightness(110%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
  }

  .glass-ultra-premium::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
      radial-gradient(
        circle at 30% 20%,
        rgba(255, 255, 255, 0.15) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 70% 80%,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 1;
  }

  .glass-ultra-premium::after {
    content: '';
    position: absolute;
    inset: 0;
    background:
      linear-gradient(
        45deg,
        transparent 0%,
        rgba(255, 255, 255, 0.05) 50%,
        transparent 100%
      );
    pointer-events: none;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .glass-ultra-premium:hover::after {
    opacity: 1;
  }

  .glass-frosted::after {
    content: '';
    position: absolute;
    inset: 0;
    background-image:
      radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 50%);
    mix-blend-mode: overlay;
    pointer-events: none;
  }

  /* Cursor effects */
  .cursor-glow {
    position: relative;
  }

  .cursor-glow::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 0, 128, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .cursor-glow:hover::before {
    opacity: 1;
  }

  /* Revolutionary 3D Transform System 2025+ */
  .transform-3d {
    transform-style: preserve-3d;
  }

  /* Enhanced Perspective utilities */
  .perspective-500 {
    perspective: 500px;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .perspective-2000 {
    perspective: 2000px;
  }

  .perspective-3000 {
    perspective: 3000px;
  }

  /* Isometric Projections */
  .iso-view {
    transform: rotateX(35.264deg) rotateY(-45deg);
    transform-style: preserve-3d;
  }

  .iso-cube {
    transform-style: preserve-3d;
    position: relative;
  }

  .iso-face-front {
    transform: translateZ(50px);
  }

  .iso-face-back {
    transform: translateZ(-50px) rotateY(180deg);
  }

  .iso-face-right {
    transform: rotateY(90deg) translateZ(50px);
  }

  .iso-face-left {
    transform: rotateY(-90deg) translateZ(50px);
  }

  .iso-face-top {
    transform: rotateX(90deg) translateZ(50px);
  }

  .iso-face-bottom {
    transform: rotateX(-90deg) translateZ(50px);
  }

  /* Depth-of-Field Blur */
  .dof-near {
    filter: blur(0px);
    z-index: 10;
  }

  .dof-mid {
    filter: blur(1px);
    z-index: 5;
  }

  .dof-far {
    filter: blur(3px);
    z-index: 1;
  }

  .dof-very-far {
    filter: blur(6px);
    z-index: 0;
  }

  /* Interactive 3D Scene */
  .scene-3d {
    perspective: 1200px;
    perspective-origin: center center;
    transform-style: preserve-3d;
    position: relative;
    overflow: hidden;
  }

  .scene-object {
    transform-style: preserve-3d;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .scene-object:hover {
    transform: translateZ(50px) rotateY(15deg) rotateX(5deg);
  }

  /* Floating 3D Cards */
  .card-3d {
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .card-3d::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    transform: translateZ(-10px);
    filter: blur(20px) brightness(0.8);
    opacity: 0.5;
    z-index: -1;
  }

  .card-3d:hover {
    transform: translateZ(30px) rotateY(10deg) rotateX(5deg);
  }

  /* Layered 3D Depth */
  .depth-layer-1 {
    transform: translateZ(10px);
  }

  .depth-layer-2 {
    transform: translateZ(20px);
  }

  .depth-layer-3 {
    transform: translateZ(30px);
  }

  .depth-layer-4 {
    transform: translateZ(40px);
  }

  .depth-layer-5 {
    transform: translateZ(50px);
  }

  /* OPTIMIZED: Simplified 3D effects - removed expensive continuous animations */
  .rotate-3d-x {
    /* REMOVED: animation: rotate-3d-x 10s linear infinite; */
    transform: rotateX(5deg);
    will-change: auto; /* Changed from transform */
  }

  .rotate-3d-y {
    /* REMOVED: animation: rotate-3d-y 8s linear infinite; */
    transform: rotateY(5deg);
    will-change: auto; /* Changed from transform */
  }

  /* OPTIMIZED: Simplified morphing 3D shapes */
  .morph-3d {
    transform-style: preserve-3d;
    /* REMOVED: animation: morph-3d-shape 8s ease-in-out infinite; */
    will-change: auto; /* Changed from transform */
  }

  /* 3D Parallax Layers */
  .parallax-3d-near {
    transform: translateZ(100px) scale(0.9);
  }

  .parallax-3d-mid {
    transform: translateZ(0px) scale(1);
  }

  .parallax-3d-far {
    transform: translateZ(-100px) scale(1.1);
  }

  /* Interactive 3D Hover */
  .hover-3d {
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .hover-3d:hover {
    transform:
      perspective(1000px)
      rotateX(var(--rotate-x, 0deg))
      rotateY(var(--rotate-y, 0deg))
      translateZ(20px);
  }

  /* 3D Text Effects */
  .text-3d {
    text-shadow:
      1px 1px 0 rgba(0, 0, 0, 0.8),
      2px 2px 0 rgba(0, 0, 0, 0.6),
      3px 3px 0 rgba(0, 0, 0, 0.4),
      4px 4px 0 rgba(0, 0, 0, 0.2),
      5px 5px 10px rgba(0, 0, 0, 0.5);
    transform: translateZ(10px);
  }

  .text-3d-neon {
    text-shadow:
      0 0 5px rgba(255, 0, 128, 0.8),
      0 0 10px rgba(255, 0, 128, 0.6),
      0 0 15px rgba(255, 0, 128, 0.4),
      0 0 20px rgba(0, 255, 255, 0.3),
      1px 1px 0 rgba(0, 0, 0, 0.8),
      2px 2px 0 rgba(0, 0, 0, 0.6);
    transform: translateZ(15px);
  }

  /* Dynamic Gradient Animations 2025+ */
  /* OPTIMIZED: Simplified gradient mesh without expensive animation */
  .gradient-mesh {
    background:
      radial-gradient(circle at 20% 20%, rgba(255, 0, 128, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.4) 0%, transparent 50%),
      linear-gradient(45deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.9));
    /* REMOVED: complex background-size and animation */
  }

  /* OPTIMIZED: Simplified gradient noise without expensive animation */
  .gradient-noise {
    background:
      linear-gradient(45deg,
        rgba(255, 0, 128, 0.4) 0%,
        rgba(0, 255, 255, 0.4) 50%,
        rgba(255, 0, 128, 0.4) 100%
      );
    /* REMOVED: background-size and animation */
    position: relative;
  }

  .gradient-noise::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
      repeating-linear-gradient(
        45deg,
        transparent 0px,
        rgba(255, 255, 255, 0.03) 1px,
        transparent 2px,
        rgba(0, 0, 0, 0.02) 3px,
        transparent 4px
      );
    /* REMOVED: animation: noise-pattern 8s linear infinite; */
    pointer-events: none;
  }

  .gradient-bleeding {
    background:
      conic-gradient(
        from 0deg,
        rgba(255, 0, 128, 0.9) 0deg,
        rgba(255, 128, 0, 0.8) 60deg,
        rgba(128, 255, 0, 0.7) 120deg,
        rgba(0, 255, 128, 0.8) 180deg,
        rgba(0, 128, 255, 0.9) 240deg,
        rgba(128, 0, 255, 0.8) 300deg,
        rgba(255, 0, 128, 0.9) 360deg
      );
    /* REMOVED: animation: gradient-bleeding-rotation 12s linear infinite; */
    filter: blur(0.5px) saturate(120%);
  }

  .gradient-reactive {
    background:
      radial-gradient(
        circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(255, 0, 128, 0.8) 0%,
        rgba(0, 255, 255, 0.6) 30%,
        rgba(255, 255, 0, 0.4) 60%,
        transparent 100%
      );
    transition: background 0.3s ease;
  }

  .gradient-liquid {
    background:
      linear-gradient(45deg,
        rgba(255, 0, 128, 0.9),
        rgba(0, 255, 255, 0.8),
        rgba(255, 255, 0, 0.7),
        rgba(255, 0, 255, 0.8)
      );
    background-size: 400% 400%;
    /* REMOVED: animation: gradient-liquid-flow 18s ease-in-out infinite; */
    filter: blur(0.3px) contrast(110%) saturate(110%);
  }

  .gradient-plasma {
    background:
      radial-gradient(ellipse at top, rgba(255, 0, 128, 0.8) 0%, transparent 70%),
      radial-gradient(ellipse at bottom, rgba(0, 255, 255, 0.8) 0%, transparent 70%),
      radial-gradient(ellipse at left, rgba(255, 255, 0, 0.6) 0%, transparent 70%),
      radial-gradient(ellipse at right, rgba(255, 0, 255, 0.7) 0%, transparent 70%);
    /* REMOVED: animation: gradient-plasma-pulse 16s ease-in-out infinite; */
  }

  .gradient-holographic {
    background:
      linear-gradient(
        45deg,
        rgba(255, 0, 128, 0.8) 0%,
        rgba(0, 255, 255, 0.8) 20%,
        rgba(255, 255, 0, 0.8) 40%,
        rgba(255, 0, 255, 0.8) 60%,
        rgba(128, 255, 0, 0.8) 80%,
        rgba(255, 0, 128, 0.8) 100%
      );
    background-size: 600% 600%;
    /* REMOVED: animation: gradient-holographic-shift 14s ease-in-out infinite; */
    filter: hue-rotate(0deg) saturate(120%) brightness(105%);
  }

  .gradient-aurora {
    background:
      linear-gradient(
        90deg,
        rgba(255, 0, 128, 0.0) 0%,
        rgba(255, 0, 128, 0.8) 20%,
        rgba(0, 255, 255, 0.6) 40%,
        rgba(255, 255, 0, 0.8) 60%,
        rgba(255, 0, 255, 0.7) 80%,
        rgba(255, 0, 128, 0.0) 100%
      );
    background-size: 300% 100%;
    /* REMOVED: animation: gradient-aurora-wave 25s ease-in-out infinite; */
    filter: blur(1px) brightness(110%);
  }

  .gradient-chromatic {
    background:
      conic-gradient(
        from 0deg at 50% 50%,
        rgba(255, 0, 0, 0.8) 0deg,
        rgba(255, 128, 0, 0.8) 60deg,
        rgba(255, 255, 0, 0.8) 120deg,
        rgba(0, 255, 0, 0.8) 180deg,
        rgba(0, 255, 255, 0.8) 240deg,
        rgba(0, 0, 255, 0.8) 300deg,
        rgba(255, 0, 255, 0.8) 360deg
      );
    /* REMOVED: animation: gradient-chromatic-spin 20s linear infinite; */
    filter: saturate(150%) contrast(105%);
  }

  .gradient-distortion {
    background:
      radial-gradient(
        ellipse 200% 100% at 50% 0%,
        rgba(255, 0, 128, 0.8) 0%,
        rgba(0, 255, 255, 0.6) 50%,
        transparent 100%
      ),
      radial-gradient(
        ellipse 200% 100% at 50% 100%,
        rgba(255, 255, 0, 0.7) 0%,
        rgba(255, 0, 255, 0.5) 50%,
        transparent 100%
      );
    /* REMOVED: animation: gradient-distortion-warp 22s ease-in-out infinite; */
  }

  /* OPTIMIZED: Simplified animations for Hyper Components */
  .animate-hyper-scroll {
    /* KEPT: This is used for logo carousel - but optimized */
    animation: hyper-scroll 45s linear infinite;
  }

  .animate-scan {
    /* OPTIMIZED: Reduced frequency */
    animation: scan 4s ease-in-out infinite;
  }

  .animate-ripple {
    /* KEPT: Short duration, not expensive */
    animation: ripple 0.6s ease-out forwards;
  }

  .animate-magnetic {
    transition: transform 0.1s ease-out;
  }

  .animate-quantum-rotation {
    /* REMOVED: animation: quantum-rotation 4s ease-in-out infinite; */
    transform: rotateY(5deg) rotateX(2deg);
  }

  .animate-plasma-pulse {
    /* OPTIMIZED: CSS-only pulse effect */
    box-shadow: 0 0 15px rgba(255, 0, 128, 0.4);
    transition: box-shadow 0.3s ease;
  }

  .animate-plasma-pulse:hover {
    box-shadow: 0 0 25px rgba(255, 0, 128, 0.6), 0 0 40px rgba(0, 255, 255, 0.3);
  }

  .animate-holographic-shift {
    /* REMOVED: animation: holographic-shift 3s ease-in-out infinite; */
    background: linear-gradient(45deg, #06b6d4, #3b82f6, #8b5cf6);
  }

  /* Removed unused particle system classes - using inline particle effects instead */

  .rotate-x-12 {
    transform: rotateX(12deg);
  }

  .rotate-y-12 {
    transform: rotateY(12deg);
  }

  /* Parallax */
  .parallax {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Advanced Neumorphism 2025+ */
  .neuro {
    background: #1a1a1a;
    border-radius: 20px;
    box-shadow:
      20px 20px 40px rgba(0, 0, 0, 0.8),
      -20px -20px 40px rgba(255, 255, 255, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .neuro-pressed {
    background: #151515;
    box-shadow:
      inset 15px 15px 30px rgba(0, 0, 0, 0.9),
      inset -15px -15px 30px rgba(255, 255, 255, 0.03),
      0 0 0 1px rgba(255, 255, 255, 0.05);
    transform: scale(0.98);
  }

  /* Dynamic Lighting Neumorphism */
  .neuro-dynamic {
    background: linear-gradient(145deg, #1e1e1e, #0f0f0f);
    border-radius: 25px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .neuro-dynamic::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
      from 0deg,
      rgba(255, 0, 128, 0.1) 0deg,
      rgba(0, 255, 255, 0.1) 120deg,
      rgba(255, 255, 0, 0.1) 240deg,
      rgba(255, 0, 128, 0.1) 360deg
    );
    /* REMOVED: animation: neuro-light-rotation 8s linear infinite; */
    pointer-events: none;
  }

  .neuro-dynamic::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: linear-gradient(145deg, #1a1a1a, #0d0d0d);
    border-radius: 23px;
    z-index: 1;
  }

  .neuro-dynamic > * {
    position: relative;
    z-index: 2;
  }

  /* Pressure-Sensitive Neumorphism */
  .neuro-pressure {
    background: #1a1a1a;
    border-radius: 20px;
    box-shadow:
      20px 20px 60px rgba(0, 0, 0, 0.7),
      -20px -20px 60px rgba(255, 255, 255, 0.04);
    transition: all 0.1s ease-out;
    cursor: pointer;
  }

  .neuro-pressure:hover {
    box-shadow:
      25px 25px 80px rgba(0, 0, 0, 0.8),
      -25px -25px 80px rgba(255, 255, 255, 0.06);
    transform: translateY(-2px);
  }

  .neuro-pressure:active {
    box-shadow:
      inset 10px 10px 20px rgba(0, 0, 0, 0.9),
      inset -10px -10px 20px rgba(255, 255, 255, 0.02);
    transform: translateY(1px) scale(0.99);
  }

  /* Textured Neumorphism */
  .neuro-textured {
    background:
      radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 0, 0, 0.3) 0%, transparent 50%),
      linear-gradient(145deg, #1c1c1c, #0e0e0e);
    border-radius: 24px;
    box-shadow:
      20px 20px 40px rgba(0, 0, 0, 0.8),
      -20px -20px 40px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.5);
    position: relative;
  }

  .neuro-textured::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
      repeating-linear-gradient(
        45deg,
        transparent 0px,
        rgba(255, 255, 255, 0.01) 1px,
        transparent 2px
      );
    border-radius: 24px;
    pointer-events: none;
  }

  /* Morphing Shadow Neumorphism */
  .neuro-morphing {
    background: #1a1a1a;
    border-radius: 20px;
    box-shadow:
      20px 20px 40px rgba(0, 0, 0, 0.8),
      -20px -20px 40px rgba(255, 255, 255, 0.05);
    /* REMOVED: animation: shadow-morph 6s ease-in-out infinite; */
  }

  /* Elevated Neumorphism */
  .neuro-elevated {
    background: linear-gradient(145deg, #1f1f1f, #0a0a0a);
    border-radius: 30px;
    box-shadow:
      30px 30px 60px rgba(0, 0, 0, 0.9),
      -30px -30px 60px rgba(255, 255, 255, 0.06),
      inset 0 2px 4px rgba(255, 255, 255, 0.1),
      inset 0 -2px 4px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
  }

  .neuro-elevated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.05) 50%,
      transparent 100%
    );
    /* REMOVED: animation: neuro-shine 4s ease-in-out infinite; */
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  /* OPTIMIZED: Hover effect for neuro-shine */
  .neuro-elevated:hover::before {
    opacity: 1;
  }

  /* Advanced Micro-Interactions 2025+ */
  .micro-haptic {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .micro-haptic:hover {
    transform: translateY(-1px) scale(1.02);
    filter: brightness(1.1);
  }

  .micro-haptic:active {
    transform: translateY(1px) scale(0.98);
    filter: brightness(0.95);
    transition: all 0.05s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Sound Visualization Effect */
  .micro-sound {
    position: relative;
    overflow: hidden;
  }

  .micro-sound::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    opacity: 0;
  }

  .micro-sound:hover::before {
    width: 200px;
    height: 200px;
    opacity: 1;
    animation: sound-ripple 0.6s ease-out;
  }

  /* Contextual Animation */
  .micro-context {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .micro-context::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg,
      rgba(255, 0, 128, 0.5),
      rgba(0, 255, 255, 0.5),
      rgba(255, 255, 0, 0.5)
    );
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    /* REMOVED: animation: context-glow 2s ease-in-out infinite; */
  }

  .micro-context:hover::after {
    opacity: 1;
  }

  /* Magnetic Attraction Effect */
  .micro-magnetic {
    transition: transform 0.1s ease-out;
    cursor: pointer;
  }

  .micro-magnetic:hover {
    animation: magnetic-pull 0.3s ease-out;
  }

  /* Pressure Sensitive */
  .micro-pressure {
    transition: all 0.1s ease-out;
    transform-origin: center;
  }

  .micro-pressure:active {
    transform: scale(0.95);
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Elastic Bounce */
  .micro-elastic {
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .micro-elastic:hover {
    transform: scale(1.1);
  }

  .micro-elastic:active {
    transform: scale(0.9);
    transition: transform 0.1s ease-out;
  }

  /* Liquid Morph */
  .micro-liquid {
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .micro-liquid:hover {
    border-radius: 50px;
    transform: scale(1.05);
  }

  .micro-liquid:active {
    border-radius: 10px;
    transform: scale(0.95);
  }

  /* Particle Burst */
  .micro-burst {
    position: relative;
    overflow: visible;
  }

  .micro-burst::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: rgba(255, 0, 128, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    pointer-events: none;
  }

  .micro-burst:active::before {
    animation: particle-burst 0.6s ease-out;
  }

  /* Glow Pulse on Interaction */
  .micro-glow-pulse {
    position: relative;
    transition: all 0.3s ease;
  }

  .micro-glow-pulse:hover {
    box-shadow:
      0 0 20px rgba(255, 0, 128, 0.4),
      0 0 40px rgba(0, 255, 255, 0.2),
      0 0 60px rgba(255, 255, 0, 0.1);
    /* REMOVED: animation: glow-pulse-interaction 1s ease-in-out infinite; */
  }

  /* Depth Shift */
  .micro-depth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
  }

  .micro-depth:hover {
    transform: translateZ(20px) rotateX(5deg) rotateY(5deg);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Advanced Dark Mode Features */
  .dark-adaptive {
    transition: all 0.5s ease;
  }

  /* Optimized time-based brightness adjustment - Consolidated into single class with CSS variables */
  .dark-time-adaptive {
    filter: brightness(var(--time-brightness, 1))
            contrast(var(--time-contrast, 1))
            saturate(var(--time-saturation, 1))
            sepia(var(--time-sepia, 0));
    transition: filter 0.5s ease;
  }

  /* Removed individual time classes - use CSS variables instead for better performance */

  /* Depth-aware shadows in dark mode */
  .dark-depth-1 {
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.8),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .dark-depth-2 {
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.9),
      0 0 0 1px rgba(255, 255, 255, 0.08);
  }

  .dark-depth-3 {
    box-shadow:
      0 8px 16px rgba(0, 0, 0, 0.95),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Neon accents for dark mode */
  .dark-neon-accent {
    border: 1px solid rgba(255, 0, 128, 0.3);
    box-shadow:
      0 0 10px rgba(255, 0, 128, 0.2),
      inset 0 0 10px rgba(255, 0, 128, 0.1);
  }

  .dark-neon-accent:hover {
    border-color: rgba(255, 0, 128, 0.6);
    box-shadow:
      0 0 20px rgba(255, 0, 128, 0.4),
      0 0 40px rgba(0, 255, 255, 0.2),
      inset 0 0 20px rgba(255, 0, 128, 0.2);
  }

  /* Ambient glow effects */
  .dark-ambient-glow {
    position: relative;
  }

  .dark-ambient-glow::before {
    content: '';
    position: absolute;
    inset: -20px;
    background: radial-gradient(
      circle,
      rgba(255, 0, 128, 0.1) 0%,
      rgba(0, 255, 255, 0.05) 50%,
      transparent 100%
    );
    border-radius: inherit;
    /* REMOVED: animation: ambient-glow-pulse 4s ease-in-out infinite; */
    pointer-events: none;
    z-index: -1;
  }

  /* Screen edge lighting */
  .dark-edge-light {
    position: fixed;
    inset: 0;
    pointer-events: none;
    z-index: -2;
    background:
      linear-gradient(0deg, rgba(255, 0, 128, 0.05) 0%, transparent 10%),
      linear-gradient(90deg, rgba(0, 255, 255, 0.03) 0%, transparent 10%),
      linear-gradient(180deg, rgba(255, 255, 0, 0.02) 0%, transparent 10%),
      linear-gradient(270deg, rgba(255, 0, 128, 0.03) 0%, transparent 10%);
    /* REMOVED: animation: edge-light-shift 12s ease-in-out infinite; */
  }

  /* Optimized contrast enhancement - Consolidated with CSS variables */
  .dark-contrast-adaptive {
    filter: contrast(var(--contrast-level, 1)) brightness(var(--brightness-level, 1));
    transition: filter 0.3s ease;
  }

  /* CSS variables for different contrast levels */
  .dark-contrast-adaptive.high { --contrast-level: 1.3; --brightness-level: 1.1; }
  .dark-contrast-adaptive.medium { --contrast-level: 1.15; --brightness-level: 1.05; }
  .dark-contrast-adaptive.low { --contrast-level: 1.05; --brightness-level: 1.02; }

  /* Advanced Event Card Keyframes */
  @keyframes particle-orbit {
    0% {
      transform: rotate(0deg) translateX(20px) rotate(0deg);
    }
    100% {
      transform: rotate(360deg) translateX(20px) rotate(-360deg);
    }
  }

  @keyframes particle-burst {
    0% {
      transform: translate(-50%, -50%) scale(0);
      opacity: 1;
    }
    50% {
      transform: translate(-50%, -50%) scale(3);
      opacity: 0.8;
    }
    100% {
      transform: translate(-50%, -50%) scale(6);
      opacity: 0;
    }
  }

  @keyframes chromatic-shift {
    0% {
      filter: hue-rotate(0deg) saturate(100%);
    }
    25% {
      filter: hue-rotate(90deg) saturate(120%);
    }
    50% {
      filter: hue-rotate(180deg) saturate(150%);
    }
    75% {
      filter: hue-rotate(270deg) saturate(120%);
    }
    100% {
      filter: hue-rotate(360deg) saturate(100%);
    }
  }

  @keyframes liquid-distortion {
    0% {
      transform: scale(1) rotate(0deg);
      filter: blur(0px);
    }
    25% {
      transform: scale(1.02) rotate(1deg);
      filter: blur(0.5px);
    }
    50% {
      transform: scale(1.05) rotate(0deg);
      filter: blur(1px);
    }
    75% {
      transform: scale(1.02) rotate(-1deg);
      filter: blur(0.5px);
    }
    100% {
      transform: scale(1) rotate(0deg);
      filter: blur(0px);
    }
  }

  @keyframes holographic-scan {
    0% {
      transform: translateY(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateY(100%);
      opacity: 0;
    }
  }

  /* Advanced Micro-Interaction Keyframes */
  @keyframes haptic-feedback {
    0% { transform: scale(1); }
    25% { transform: scale(0.95); }
    50% { transform: scale(1.05); }
    75% { transform: scale(0.98); }
    100% { transform: scale(1); }
  }

  /* REMOVED: Unused keyframes for sound-wave, magnetic-attraction, and contextual-glow */

  @keyframes pressure-response {
    0% {
      transform: scale(1) translateZ(0px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
      transform: scale(0.95) translateZ(-5px);
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.5);
    }
    100% {
      transform: scale(1) translateZ(0px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
  }

  /* Ultra-Advanced Micro-Interaction Classes */
  .micro-haptic-advanced {
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .micro-haptic-advanced:active {
    animation: haptic-feedback 0.2s ease-out;
  }

  .micro-sound-wave {
    position: relative;
    overflow: hidden;
  }

  .micro-sound-wave::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    pointer-events: none;
  }

  .micro-sound-wave:hover::before {
    /* OPTIMIZED: Simple scale effect instead of expensive animation */
    transform: scale(1.1);
    opacity: 0.8;
  }

  .micro-magnetic-field {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .micro-magnetic-field:hover {
    /* OPTIMIZED: Simple transform instead of expensive animation */
    transform: translateY(-2px) scale(1.02);
  }

  .micro-contextual-response {
    transition: all 0.3s ease;
    position: relative;
  }

  /* OPTIMIZED: Simplified micro-interactions without expensive animations */
  .micro-contextual-response:hover {
    /* REMOVED: animation: contextual-glow 2s ease-in-out infinite; */
    box-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
  }

  .micro-pressure-sensitive {
    transition: transform 0.1s ease-out;
    /* REMOVED: transform-style: preserve-3d; */
  }

  .micro-pressure-sensitive:active {
    /* REMOVED: animation: pressure-response 0.3s ease-out; */
    transform: scale(0.95);
  }

  /* Velocity-Based Interactions */
  .micro-velocity-responsive {
    transition: transform 0.1s ease-out;
  }

  .micro-velocity-responsive:hover {
    transform: translateZ(10px) scale(1.05);
  }

  /* Neural Network Visualization */
  .micro-neural {
    position: relative;
    overflow: hidden;
  }

  .micro-neural::after {
    content: '';
    position: absolute;
    inset: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 30%),
      radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 30%),
      radial-gradient(circle at 50% 50%, rgba(255, 255, 0, 0.05) 0%, transparent 40%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  /* OPTIMIZED: Simplified neural effect without expensive animation */
  .micro-neural:hover::after {
    opacity: 0.8;
    /* REMOVED: animation: neural-pulse 1.5s ease-in-out infinite; */
  }

  /* REMOVED: Unused keyframe for neural-pulse */
}
