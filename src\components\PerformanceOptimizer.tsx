'use client';

import { useEffect } from 'react';
import { performanceManager } from '../utils/PerformanceManager';

/**
 * Performance optimizer to prevent "settling" behavior on page load
 * This component coordinates the timing of animations and asset loading
 */
export default function PerformanceOptimizer() {
  useEffect(() => {
    // Initialize the global performance manager
    performanceManager.initialize();
  }, []); // Empty dependency array is safe here since initialize() handles duplicates

  return null; // This component doesn't render anything
}
