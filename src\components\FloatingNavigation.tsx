'use client';

import { useState, useEffect, useRef } from 'react';
import { useAnimation } from '../utils/AnimationManager';

interface FloatingNavProps {
  sections: Array<{
    id: string;
    label: string;
    icon: string;
  }>;
}

export default function FloatingNavigation({ sections }: FloatingNavProps) {
  const [activeSection, setActiveSection] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [time, setTime] = useState(0);

  useAnimation('floating-nav', (currentTime) => {
    setTime(currentTime);
  }, 15);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min(scrollTop / docHeight, 1);
      setScrollProgress(progress);

      // Show navigation after scrolling past hero
      setIsVisible(scrollTop > window.innerHeight * 0.3);

      // Update active section based on scroll position
      const sectionElements = sections.map(section => 
        document.getElementById(section.id)
      ).filter(Boolean);

      let currentSection = 0;
      sectionElements.forEach((element, index) => {
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= window.innerHeight / 2) {
            currentSection = index;
          }
        }
      });
      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, [sections]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Floating Progress Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-black/20 backdrop-blur-sm">
        <div 
          className="h-full bg-gradient-to-r from-neon-pink via-neon-cyan to-neon-green transition-all duration-300"
          style={{ width: `${scrollProgress * 100}%` }}
        />
      </div>

      {/* Floating Section Navigator */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-40">
        <div 
          className="glass-ultra rounded-2xl p-4 border border-white/20 space-y-3"
          style={{
            transform: `translateY(${Math.sin(time * 0.2) * 3}px)`,
          }}
        >
          {sections.map((section, index) => (
            <button
              key={section.id}
              onClick={() => scrollToSection(section.id)}
              className={`
                relative group w-12 h-12 rounded-xl flex items-center justify-center
                transition-all duration-300 hover:scale-110
                ${activeSection === index 
                  ? 'bg-gradient-to-r from-neon-pink to-neon-cyan text-black' 
                  : 'bg-white/10 text-white/60 hover:bg-white/20 hover:text-white'
                }
              `}
              title={section.label}
            >
              <span className="text-lg">{section.icon}</span>
              
              {/* Active Indicator */}
              {activeSection === index && (
                <div className="absolute -right-2 top-1/2 transform -translate-y-1/2">
                  <div className="w-2 h-2 bg-neon-cyan rounded-full animate-pulse" />
                </div>
              )}

              {/* Hover Tooltip */}
              <div className="absolute right-full mr-4 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="glass rounded-lg px-3 py-2 border border-white/20 whitespace-nowrap">
                  <span className="text-white text-sm font-medium">{section.label}</span>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Floating Back to Top */}
      {scrollProgress > 0.2 && (
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-8 right-8 z-40 w-14 h-14 bg-gradient-to-r from-neon-pink to-neon-cyan rounded-full flex items-center justify-center text-black font-black text-xl hover:scale-110 transition-all duration-300 animate-float-slow"
          style={{
            transform: `translateY(${Math.sin(time * 0.3) * 2}px) scale(${scrollProgress > 0.8 ? 1.1 : 1})`,
          }}
        >
          ↑
        </button>
      )}

      {/* Floating Mini Stats */}
      <div className="fixed left-8 bottom-8 z-40">
        <div 
          className="glass-ultra rounded-2xl p-4 border border-white/20 space-y-2"
          style={{
            transform: `translateY(${Math.sin(time * 0.15) * 2}px)`,
            opacity: scrollProgress > 0.1 ? 1 : 0,
          }}
        >
          <div className="text-center">
            <div className="text-2xl font-black text-neon-cyan">
              {Math.round(scrollProgress * 100)}%
            </div>
            <div className="text-white/60 text-xs font-medium">Explored</div>
          </div>
          <div className="w-full h-1 bg-white/20 rounded-full overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-neon-pink to-neon-cyan transition-all duration-300"
              style={{ width: `${scrollProgress * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Floating Section Preview */}
      {activeSection < sections.length && (
        <div className="fixed left-8 top-1/2 transform -translate-y-1/2 z-30">
          <div 
            className="glass rounded-xl p-3 border border-white/20 max-w-xs opacity-80 hover:opacity-100 transition-opacity duration-300"
            style={{
              transform: `translateX(${Math.sin(time * 0.1) * 5}px)`,
            }}
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-neon-pink to-neon-cyan rounded-lg flex items-center justify-center text-black font-bold text-sm">
                {sections[activeSection]?.icon}
              </div>
              <div>
                <div className="text-white font-bold text-sm">
                  {sections[activeSection]?.label}
                </div>
                <div className="text-white/60 text-xs">
                  Section {activeSection + 1} of {sections.length}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Magnetic Cursor Effect Area */}
      <div className="fixed inset-0 pointer-events-none z-20">
        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${10 + (i * 12)}%`,
              top: `${20 + Math.sin(time * 0.3 + i) * 30}%`,
              transform: `translateY(${Math.sin(time * 0.2 + i) * 20}px)`,
              opacity: 0.3 + Math.sin(time + i) * 0.2,
            }}
          />
        ))}
      </div>
    </>
  );
}

// Hook for easy integration
export function useFloatingNavigation() {
  const sections = [
    { id: 'about-hero', label: 'About', icon: '🏠' },
    { id: 'our-story', label: 'Story', icon: '📖' },
    { id: 'team-section', label: 'Team', icon: '👥' },
    { id: 'values-section', label: 'Values', icon: '💎' },
    { id: 'experience-showcase', label: 'Experiences', icon: '🎨' },
    { id: 'stats-section', label: 'Impact', icon: '📊' },
    { id: 'partner-with-us', label: 'Partner', icon: '🤝' },
  ];

  return { sections };
}
