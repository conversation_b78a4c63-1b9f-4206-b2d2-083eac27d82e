'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  animationCount: number;
  mouseEventCount: number;
  renderTime: number;
  cpuUsage: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  compact?: boolean;
  onPerformanceChange?: (metrics: PerformanceMetrics) => void;
}

export default function PerformanceMonitor({ 
  enabled = true, 
  position = 'top-left',
  compact = false,
  onPerformanceChange 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    frameTime: 0,
    memoryUsage: 0,
    animationCount: 0,
    mouseEventCount: 0,
    renderTime: 0,
    cpuUsage: 0
  });

  const [isVisible, setIsVisible] = useState(enabled);
  const [performanceGrade, setPerformanceGrade] = useState<'A' | 'B' | 'C' | 'D' | 'F'>('A');

  // Performance tracking refs
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const frameTimeHistory = useRef<number[]>([]);
  const mouseEventCount = useRef(0);
  const animationFrameId = useRef<number>();
  const renderStartTime = useRef(0);
  const memoryCheckInterval = useRef<NodeJS.Timeout>();

  // FPS and frame time calculation
  const calculateFPS = useCallback(() => {
    const now = performance.now();
    const deltaTime = now - lastTime.current;
    
    frameCount.current++;
    frameTimeHistory.current.push(deltaTime);
    
    // Keep only last 60 frames for rolling average
    if (frameTimeHistory.current.length > 60) {
      frameTimeHistory.current.shift();
    }

    // Calculate FPS every 60 frames or 1 second
    if (frameCount.current >= 60 || deltaTime >= 1000) {
      const avgFrameTime = frameTimeHistory.current.reduce((a, b) => a + b, 0) / frameTimeHistory.current.length;
      const fps = Math.round(1000 / avgFrameTime);
      
      setMetrics(prev => ({
        ...prev,
        fps: Math.min(fps, 60), // Cap at 60fps
        frameTime: Math.round(avgFrameTime * 100) / 100
      }));

      frameCount.current = 0;
      lastTime.current = now;
    }
  }, []);

  // Memory usage monitoring
  const checkMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      
      setMetrics(prev => ({
        ...prev,
        memoryUsage: usedMB
      }));
    }
  }, []);

  // Animation count monitoring
  const countActiveAnimations = useCallback(() => {
    const animatedElements = document.querySelectorAll('[style*="animation"], [style*="transform"], .animate-');
    const runningAnimations = document.getAnimations ? document.getAnimations().length : animatedElements.length;
    
    setMetrics(prev => ({
      ...prev,
      animationCount: runningAnimations
    }));
  }, []);

  // Mouse event tracking
  useEffect(() => {
    const trackMouseEvents = () => {
      mouseEventCount.current++;
    };

    const resetMouseEventCount = () => {
      setMetrics(prev => ({
        ...prev,
        mouseEventCount: mouseEventCount.current
      }));
      mouseEventCount.current = 0;
    };

    if (enabled) {
      window.addEventListener('mousemove', trackMouseEvents, { passive: true });
      window.addEventListener('mousedown', trackMouseEvents, { passive: true });
      window.addEventListener('mouseup', trackMouseEvents, { passive: true });
      
      const interval = setInterval(resetMouseEventCount, 1000);
      
      return () => {
        window.removeEventListener('mousemove', trackMouseEvents);
        window.removeEventListener('mousedown', trackMouseEvents);
        window.removeEventListener('mouseup', trackMouseEvents);
        clearInterval(interval);
      };
    }
  }, [enabled]);

  // Main performance monitoring loop
  useEffect(() => {
    if (!enabled) return;

    const monitorPerformance = () => {
      renderStartTime.current = performance.now();
      
      calculateFPS();
      countActiveAnimations();
      
      // Calculate render time
      const renderTime = performance.now() - renderStartTime.current;
      setMetrics(prev => ({
        ...prev,
        renderTime: Math.round(renderTime * 100) / 100
      }));

      animationFrameId.current = requestAnimationFrame(monitorPerformance);
    };

    // Start monitoring
    animationFrameId.current = requestAnimationFrame(monitorPerformance);
    
    // Memory check every 2 seconds
    memoryCheckInterval.current = setInterval(checkMemoryUsage, 2000);

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
      if (memoryCheckInterval.current) {
        clearInterval(memoryCheckInterval.current);
      }
    };
  }, [enabled, calculateFPS, checkMemoryUsage, countActiveAnimations]);

  // Performance grading
  useEffect(() => {
    const { fps, frameTime, memoryUsage, animationCount } = metrics;
    
    let score = 100;
    
    // FPS scoring (40% weight)
    if (fps < 30) score -= 40;
    else if (fps < 45) score -= 20;
    else if (fps < 55) score -= 10;
    
    // Frame time scoring (30% weight)
    if (frameTime > 33) score -= 30; // > 33ms = < 30fps
    else if (frameTime > 22) score -= 15; // > 22ms = < 45fps
    else if (frameTime > 18) score -= 8; // > 18ms = < 55fps
    
    // Memory usage scoring (20% weight)
    if (memoryUsage > 100) score -= 20;
    else if (memoryUsage > 50) score -= 10;
    else if (memoryUsage > 25) score -= 5;
    
    // Animation count scoring (10% weight)
    if (animationCount > 20) score -= 10;
    else if (animationCount > 10) score -= 5;
    
    // Determine grade
    let grade: typeof performanceGrade;
    if (score >= 90) grade = 'A';
    else if (score >= 80) grade = 'B';
    else if (score >= 70) grade = 'C';
    else if (score >= 60) grade = 'D';
    else grade = 'F';
    
    setPerformanceGrade(grade);
    
    // Notify parent component
    if (onPerformanceChange) {
      onPerformanceChange(metrics);
    }
  }, [metrics, onPerformanceChange]);

  // Toggle visibility with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  if (!enabled || !isVisible) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-400';
      case 'B': return 'text-yellow-400';
      case 'C': return 'text-orange-400';
      case 'D': return 'text-red-400';
      case 'F': return 'text-red-600';
      default: return 'text-gray-400';
    }
  };

  const getMetricColor = (value: number, thresholds: [number, number]) => {
    if (value <= thresholds[0]) return 'text-green-400';
    if (value <= thresholds[1]) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-[10000] select-none`}>
      <div className="bg-black/80 backdrop-blur-sm border border-white/20 rounded-lg p-3 font-mono text-xs">
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-white/60">Performance</span>
          <div className="flex items-center space-x-2">
            <span className={`font-bold ${getGradeColor(performanceGrade)}`}>
              {performanceGrade}
            </span>
            <button
              onClick={() => setIsVisible(false)}
              className="text-white/40 hover:text-white/80 transition-colors"
            >
              ×
            </button>
          </div>
        </div>

        {/* Metrics */}
        {!compact ? (
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-white/60">FPS:</span>
              <span className={getMetricColor(60 - metrics.fps, [15, 30])}>
                {metrics.fps}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Frame:</span>
              <span className={getMetricColor(metrics.frameTime, [16.7, 33.3])}>
                {metrics.frameTime}ms
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Memory:</span>
              <span className={getMetricColor(metrics.memoryUsage, [25, 50])}>
                {metrics.memoryUsage}MB
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Animations:</span>
              <span className={getMetricColor(metrics.animationCount, [5, 10])}>
                {metrics.animationCount}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Mouse/s:</span>
              <span className={getMetricColor(metrics.mouseEventCount, [100, 200])}>
                {metrics.mouseEventCount}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Render:</span>
              <span className={getMetricColor(metrics.renderTime, [1, 5])}>
                {metrics.renderTime}ms
              </span>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className={`text-lg font-bold ${getGradeColor(performanceGrade)}`}>
              {metrics.fps} FPS
            </div>
            <div className="text-white/60 text-xs">
              {metrics.frameTime}ms • {metrics.memoryUsage}MB
            </div>
          </div>
        )}

        {/* Help text */}
        <div className="mt-2 pt-2 border-t border-white/10 text-white/40 text-xs">
          Ctrl+Shift+P to toggle
        </div>
      </div>
    </div>
  );
}
