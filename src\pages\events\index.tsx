import Layout from '../../components/Layout';
import dynamic from 'next/dynamic';

// Load client-only components to avoid SSR issues
const EventsContent = dynamic(() => import('../../components/EventsContent'), { ssr: false });

export default function EventsPage() {
  return (
    <Layout
      title="Events | Conscious Collectiv"
      description="Discover upcoming conscious events, experiences, and gatherings in Southern California. Join our community for beach parties, spiritual gatherings, and transformative experiences."
      url="https://consciouscollectiv.com/events"
    >
      <EventsContent />
    </Layout>
  );
}

// Disable static generation for this page to avoid SSR issues
export async function getServerSideProps() {
  return {
    props: {},
  };
}